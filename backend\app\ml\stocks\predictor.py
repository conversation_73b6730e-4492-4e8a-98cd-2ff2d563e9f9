"""
Stock Price Predictor using LSTM neural networks
"""

import numpy as np
import os
import json
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
import logging

from .data_preprocessor import StockDataPreprocessor
from .lstm_model import StockLSTMModel
from app.services.stock_service import stock_service

logger = logging.getLogger(__name__)


class StockPredictor:
    """
    Main class for stock price prediction using LSTM models
    """
    
    def __init__(self, model_dir: str = "models/stocks"):
        """
        Initialize the stock predictor
        
        Args:
            model_dir: Directory to store trained models
        """
        self.model_dir = model_dir
        self.preprocessor = StockDataPreprocessor(sequence_length=60, prediction_days=30)
        self.model = None
        self.symbol = None
        self.last_training_date = None
        
        # Create model directory
        os.makedirs(model_dir, exist_ok=True)
        
    def _get_model_path(self, symbol: str) -> str:
        """Get the file path for a symbol's model"""
        return os.path.join(self.model_dir, f"{symbol.lower()}_lstm_model.json")
    
    def _should_retrain_model(self, symbol: str) -> bool:
        """
        Determine if model should be retrained
        
        Args:
            symbol: Stock symbol
            
        Returns:
            True if model should be retrained
        """
        model_path = self._get_model_path(symbol)
        
        # If no model exists, need to train
        if not os.path.exists(model_path):
            return True
        
        # Check if model is older than 7 days
        try:
            with open(model_path, 'r') as f:
                model_data = json.load(f)
            
            if 'timestamp' in model_data:
                last_update = datetime.fromisoformat(model_data['timestamp'])
                days_old = (datetime.now() - last_update).days
                return days_old > 7
        except Exception as e:
            logger.error(f"Error checking model age: {e}")
            return True
        
        return False
    
    async def prepare_training_data(self, symbol: str, days_back: int = 365) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare training data for the model
        
        Args:
            symbol: Stock symbol
            days_back: Number of days of historical data to fetch
            
        Returns:
            Tuple of (X, y) training data
        """
        logger.info(f"Preparing training data for {symbol}")
        
        # Fetch historical data
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        historical_data = await stock_service.get_historical_data(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            interval="1d"
        )
        
        if not historical_data.get('data'):
            raise ValueError(f"No historical data available for {symbol}")
        
        # Preprocess data
        processed_data, df = self.preprocessor.preprocess_stock_data(historical_data['data'])
        
        # Create sequences
        X, y = self.preprocessor.prepare_sequences(processed_data)
        
        logger.info(f"Prepared {len(X)} training sequences for {symbol}")
        return X, y
    
    async def train_model(self, symbol: str, force_retrain: bool = False) -> Dict:
        """
        Train LSTM model for a specific stock symbol
        
        Args:
            symbol: Stock symbol to train model for
            force_retrain: Force retraining even if model exists
            
        Returns:
            Training results dictionary
        """
        symbol = symbol.upper()
        
        # Check if retraining is needed
        if not force_retrain and not self._should_retrain_model(symbol):
            logger.info(f"Model for {symbol} is up to date, skipping training")
            return {"status": "skipped", "reason": "model_up_to_date"}
        
        try:
            # Prepare training data
            X, y = await self.prepare_training_data(symbol)
            
            if len(X) < 100:  # Need minimum data for training
                raise ValueError(f"Insufficient data for training. Need at least 100 samples, got {len(X)}")
            
            # Initialize model
            input_size = X.shape[2]  # Number of features
            self.model = StockLSTMModel(
                input_size=input_size,
                hidden_size=50,
                num_layers=2,
                output_size=1
            )
            
            # Train model
            logger.info(f"Starting training for {symbol}")
            history = self.model.train(X, y, epochs=50, validation_split=0.2)
            
            # Save model
            model_path = self._get_model_path(symbol)
            self.model.save_model(model_path)
            
            self.symbol = symbol
            self.last_training_date = datetime.now()
            
            logger.info(f"Training completed for {symbol}")
            
            return {
                "status": "success",
                "symbol": symbol,
                "training_samples": len(X),
                "final_train_loss": history['train_loss'][-1],
                "final_val_loss": history['val_loss'][-1],
                "model_path": model_path,
                "training_date": self.last_training_date.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error training model for {symbol}: {e}")
            return {
                "status": "error",
                "symbol": symbol,
                "error": str(e)
            }
    
    async def load_model(self, symbol: str) -> bool:
        """
        Load trained model for a symbol
        
        Args:
            symbol: Stock symbol
            
        Returns:
            True if model loaded successfully
        """
        symbol = symbol.upper()
        model_path = self._get_model_path(symbol)
        
        if not os.path.exists(model_path):
            logger.warning(f"No trained model found for {symbol}")
            return False
        
        # Initialize model with default parameters
        self.model = StockLSTMModel(input_size=18, hidden_size=50, num_layers=2, output_size=1)
        
        # Load model parameters
        success = self.model.load_model(model_path)
        if success:
            self.symbol = symbol
            logger.info(f"Model loaded for {symbol}")
        
        return success
    
    async def predict_prices(self, symbol: str, days_ahead: int = 30) -> Dict:
        """
        Predict stock prices for the specified number of days
        
        Args:
            symbol: Stock symbol
            days_ahead: Number of days to predict ahead
            
        Returns:
            Prediction results dictionary
        """
        symbol = symbol.upper()
        
        try:
            # Load model if not already loaded or if different symbol
            if self.model is None or self.symbol != symbol:
                model_loaded = await self.load_model(symbol)
                if not model_loaded:
                    # Train model if it doesn't exist
                    logger.info(f"No model found for {symbol}, training new model")
                    training_result = await self.train_model(symbol)
                    if training_result["status"] != "success":
                        raise ValueError(f"Failed to train model: {training_result.get('error', 'Unknown error')}")
            
            # Get recent data for prediction
            end_date = datetime.now()
            start_date = end_date - timedelta(days=90)  # Get more data for better context
            
            historical_data = await stock_service.get_historical_data(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                interval="1d"
            )
            
            if not historical_data.get('data'):
                raise ValueError(f"No recent data available for {symbol}")
            
            # Preprocess recent data
            processed_data, df = self.preprocessor.preprocess_stock_data(historical_data['data'])
            
            # Prepare prediction sequence
            prediction_input = self.preprocessor.prepare_prediction_sequences(processed_data)
            
            # Generate predictions
            predictions = []
            prediction_dates = []
            current_input = prediction_input.copy()
            
            for day in range(days_ahead):
                # Predict next day
                pred = self.model.predict(current_input)
                
                # Denormalize prediction (assuming first feature is close price)
                denormalized_pred = self.preprocessor.denormalize_data(pred, 'close')[0]
                predictions.append(float(denormalized_pred))
                
                # Calculate prediction date
                pred_date = end_date + timedelta(days=day + 1)
                prediction_dates.append(pred_date.strftime("%Y-%m-%d"))
                
                # Update input for next prediction (simplified approach)
                # In practice, you'd update with the predicted values
                if day < days_ahead - 1:
                    # Shift the sequence and add predicted value
                    new_input = current_input[0, 1:, :].copy()
                    # Create new row with predicted price and estimated other features
                    new_row = current_input[0, -1, :].copy()
                    new_row[0] = pred[0]  # Update close price
                    
                    # Add the new row
                    new_input = np.vstack([new_input, new_row.reshape(1, -1)])
                    current_input = new_input.reshape(1, new_input.shape[0], new_input.shape[1])
            
            # Get current price for reference
            current_data = await stock_service.get_stock_data(symbol)
            current_price = current_data.get('current_price', predictions[0]) if current_data else predictions[0]
            
            # Calculate confidence score based on recent model performance
            confidence_score = self._calculate_confidence_score(predictions, current_price)
            
            return {
                "symbol": symbol,
                "current_price": current_price,
                "predicted_prices": predictions,
                "prediction_dates": prediction_dates,
                "confidence_score": confidence_score,
                "model_version": "v1.0.0",
                "created_at": datetime.now(),
                "days_ahead": days_ahead
            }
            
        except Exception as e:
            logger.error(f"Error predicting prices for {symbol}: {e}")
            raise ValueError(f"Prediction failed: {str(e)}")
    
    def _calculate_confidence_score(self, predictions: List[float], current_price: float) -> float:
        """
        Calculate confidence score for predictions
        
        Args:
            predictions: List of predicted prices
            current_price: Current stock price
            
        Returns:
            Confidence score between 0 and 1
        """
        if not predictions:
            return 0.5
        
        # Calculate prediction volatility
        pred_volatility = np.std(predictions) / np.mean(predictions) if np.mean(predictions) > 0 else 1.0
        
        # Calculate trend consistency
        price_changes = np.diff(predictions)
        trend_consistency = 1.0 - (np.std(price_changes) / (np.mean(np.abs(price_changes)) + 1e-8))
        
        # Base confidence on model training status and data quality
        base_confidence = 0.8 if self.model and self.model.is_trained else 0.6
        
        # Adjust based on volatility and trend consistency
        volatility_factor = max(0.5, 1.0 - pred_volatility)
        trend_factor = max(0.5, trend_consistency)
        
        confidence = base_confidence * volatility_factor * trend_factor
        return max(0.1, min(0.95, confidence))  # Clamp between 0.1 and 0.95
    
    def get_model_info(self, symbol: str) -> Dict:
        """
        Get information about the model for a symbol
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Model information dictionary
        """
        symbol = symbol.upper()
        model_path = self._get_model_path(symbol)
        
        info = {
            "symbol": symbol,
            "model_exists": os.path.exists(model_path),
            "model_path": model_path
        }
        
        if info["model_exists"]:
            try:
                with open(model_path, 'r') as f:
                    model_data = json.load(f)
                info.update(model_data)
            except Exception as e:
                info["error"] = f"Error reading model file: {e}"
        
        return info


# Global instance
stock_predictor = StockPredictor()
