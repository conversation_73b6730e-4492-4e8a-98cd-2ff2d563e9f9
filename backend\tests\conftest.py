"""
Test configuration and fixtures
"""

import pytest
import asyncio
from typing import Generator, AsyncGenerator
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.core.database import Base, get_db
from app.models.user import User, SubscriptionTier
from app.core.auth import get_password_hash, create_access_token
from app.services.subscription_service import SubscriptionService


# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test."""
    Base.metadata.create_all(bind=engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session):
    """Create a test client with database dependency override."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()


@pytest.fixture
def test_user(db_session) -> User:
    """Create a test user."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("testpassword123"),
        full_name="Test User",
        is_active=True,
        is_verified=True,
        subscription_tier=SubscriptionTier.FREE
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def premium_user(db_session) -> User:
    """Create a premium test user."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("premiumpassword123"),
        full_name="Premium User",
        is_active=True,
        is_verified=True,
        subscription_tier=SubscriptionTier.PREMIUM
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def admin_user(db_session) -> User:
    """Create an admin test user."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("adminpassword123"),
        full_name="Admin User",
        is_active=True,
        is_verified=True,
        subscription_tier=SubscriptionTier.ENTERPRISE
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def auth_headers(test_user) -> dict:
    """Create authentication headers for test user."""
    access_token = create_access_token(data={"sub": test_user.email})
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def premium_auth_headers(premium_user) -> dict:
    """Create authentication headers for premium user."""
    access_token = create_access_token(data={"sub": premium_user.email})
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def admin_auth_headers(admin_user) -> dict:
    """Create authentication headers for admin user."""
    access_token = create_access_token(data={"sub": admin_user.email})
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def subscription_service(db_session) -> SubscriptionService:
    """Create a subscription service instance."""
    return SubscriptionService(db_session)


# Test data fixtures
@pytest.fixture
def sample_stock_data():
    """Sample stock data for testing."""
    return {
        "symbol": "AAPL",
        "current_price": 150.25,
        "predicted_prices": [151.0, 152.5, 154.0, 155.2, 156.8],
        "prediction_dates": ["2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-06"],
        "confidence_score": 0.85,
        "model_accuracy": 0.78
    }


@pytest.fixture
def sample_crypto_data():
    """Sample crypto data for testing."""
    return {
        "symbol": "BTC",
        "current_price": 45000.0,
        "predicted_prices": [45500.0, 46000.0, 46800.0, 47200.0, 47900.0],
        "prediction_dates": ["2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-06"],
        "confidence_score": 0.82,
        "model_accuracy": 0.75
    }


@pytest.fixture
def sample_prediction_request():
    """Sample prediction request data."""
    return {
        "symbol": "AAPL",
        "days_ahead": 5,
        "include_volume_analysis": True,
        "include_sentiment_analysis": False
    }


# Mock data for external APIs
@pytest.fixture
def mock_yfinance_data():
    """Mock yfinance data."""
    return {
        "Close": [150.0, 151.0, 149.5, 152.0, 153.5],
        "Volume": [1000000, 1100000, 950000, 1200000, 1150000],
        "High": [151.5, 152.0, 150.0, 153.0, 154.0],
        "Low": [149.0, 150.0, 148.5, 151.0, 152.0],
        "Open": [150.5, 150.8, 150.2, 151.5, 152.8]
    }


@pytest.fixture
def mock_coingecko_data():
    """Mock CoinGecko data."""
    return {
        "bitcoin": {
            "usd": 45000.0,
            "usd_24h_change": 2.5,
            "usd_market_cap": 850000000000,
            "usd_24h_vol": 25000000000
        }
    }


# Rate limiting test fixtures
@pytest.fixture
def rate_limit_headers():
    """Expected rate limit headers."""
    return {
        "X-RateLimit-Limit": "100",
        "X-RateLimit-Remaining": "99",
        "X-RateLimit-Reset": "3600"
    }


# Async test helpers
@pytest.fixture
async def async_client(db_session):
    """Create an async test client."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db
    
    from httpx import AsyncClient
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac
    
    app.dependency_overrides.clear()


# Database cleanup helpers
@pytest.fixture(autouse=True)
def cleanup_database(db_session):
    """Automatically cleanup database after each test."""
    yield
    # Cleanup is handled by the db_session fixture
    pass


# Environment setup
@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch):
    """Setup test environment variables."""
    monkeypatch.setenv("TESTING", "true")
    monkeypatch.setenv("SECRET_KEY", "test-secret-key-for-testing-only")
    monkeypatch.setenv("ALGORITHM", "HS256")
    monkeypatch.setenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30")
    yield


# Performance testing fixtures
@pytest.fixture
def performance_timer():
    """Timer for performance testing."""
    import time
    
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self):
            self.end_time = time.time()
        
        @property
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
    
    return Timer()
