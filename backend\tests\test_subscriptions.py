"""
Tests for subscription management functionality
"""

import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session

from app.models.user import User, SubscriptionTier
from app.services.subscription_service import SubscriptionService


class TestSubscriptionService:
    """Test subscription service functionality."""
    
    def test_get_tier_config(self, subscription_service: SubscriptionService, test_user):
        """Test getting tier configuration."""
        config = subscription_service.get_user_tier_config(test_user)
        
        assert config is not None
        assert config["api_calls_limit"] == 100
        assert config["predictions_per_day"] == 5
        assert config["max_days_ahead"] == 30
        assert "basic_predictions" in config["features"]
    
    def test_validate_api_call_within_limit(self, subscription_service: SubscriptionService, test_user: User):
        """Test API call validation within limits."""
        # Reset user's API usage
        test_user.api_calls_used = 50  # Within FREE tier limit of 100
        
        result = subscription_service.validate_api_call(test_user)
        assert result is True
    
    def test_validate_api_call_exceeds_limit(self, subscription_service: SubscriptionService, test_user: User):
        """Test API call validation when exceeding limits."""
        # Set user's API usage to exceed limit
        test_user.api_calls_used = 150  # Exceeds FREE tier limit of 100
        
        with pytest.raises(Exception) as exc_info:
            subscription_service.validate_api_call(test_user)
        assert "API call limit exceeded" in str(exc_info.value)
    
    def test_validate_prediction_within_limit(self, subscription_service: SubscriptionService, test_user: User):
        """Test prediction validation within daily limits."""
        result = subscription_service.validate_prediction(test_user, days_ahead=30)
        assert result is True
    
    def test_validate_prediction_exceeds_days_limit(self, subscription_service: SubscriptionService, test_user: User):
        """Test prediction validation when exceeding days ahead limit."""
        with pytest.raises(Exception) as exc_info:
            subscription_service.validate_prediction(test_user, days_ahead=60)  # Exceeds FREE tier limit of 30
        assert "days ahead limit" in str(exc_info.value)
    
    def test_validate_feature_access_allowed(self, subscription_service: SubscriptionService, test_user: User):
        """Test feature access validation for allowed features."""
        result = subscription_service.validate_feature_access(test_user, "basic_predictions")
        assert result is True
    
    def test_validate_feature_access_denied(self, subscription_service: SubscriptionService, test_user: User):
        """Test feature access validation for denied features."""
        with pytest.raises(Exception) as exc_info:
            subscription_service.validate_feature_access(test_user, "advanced_analytics")
        assert "feature not available" in str(exc_info.value)
    
    def test_increment_api_usage(self, subscription_service: SubscriptionService, test_user: User, db_session: Session):
        """Test incrementing API usage."""
        initial_usage = test_user.api_calls_used
        subscription_service.increment_api_usage(test_user)
        
        db_session.refresh(test_user)
        assert test_user.api_calls_used == initial_usage + 1
    
    def test_upgrade_subscription(self, subscription_service: SubscriptionService, test_user: User, db_session: Session):
        """Test subscription upgrade."""
        initial_tier = test_user.subscription_tier
        updated_user = subscription_service.upgrade_subscription(test_user, SubscriptionTier.PREMIUM)
        
        assert updated_user.subscription_tier == SubscriptionTier.PREMIUM
        assert updated_user.subscription_tier != initial_tier
    
    def test_get_usage_stats(self, subscription_service: SubscriptionService, test_user: User):
        """Test getting usage statistics."""
        stats = subscription_service.get_usage_stats(test_user)
        
        required_fields = [
            "subscription_tier", "api_calls_used", "api_calls_limit",
            "api_usage_percent", "daily_predictions_used", "daily_predictions_limit",
            "daily_usage_percent", "max_days_ahead", "features", "price"
        ]
        
        for field in required_fields:
            assert field in stats
        
        assert stats["subscription_tier"] == test_user.subscription_tier.value
        assert stats["api_calls_used"] == test_user.api_calls_used
    
    def test_reset_monthly_usage(self, subscription_service: SubscriptionService, test_user: User, db_session: Session):
        """Test resetting monthly usage."""
        # Set some usage
        test_user.api_calls_used = 50
        db_session.commit()
        
        subscription_service.reset_monthly_usage(test_user)
        db_session.refresh(test_user)
        
        assert test_user.api_calls_used == 0


class TestSubscriptionEndpoints:
    """Test subscription management endpoints."""
    
    def test_get_usage_stats(self, client: TestClient, auth_headers: dict):
        """Test getting usage statistics endpoint."""
        response = client.get("/api/v1/subscriptions/usage", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "subscription_tier" in data
        assert "api_calls_used" in data
        assert "api_calls_limit" in data
        assert "features" in data
    
    def test_get_subscription_tiers(self, client: TestClient):
        """Test getting all subscription tiers."""
        response = client.get("/api/v1/subscriptions/tiers")
        assert response.status_code == 200
        
        data = response.json()
        assert "tiers" in data
        
        tiers = data["tiers"]
        assert "free" in tiers
        assert "basic" in tiers
        assert "premium" in tiers
        assert "enterprise" in tiers
        
        # Check tier structure
        free_tier = tiers["free"]
        assert "price" in free_tier
        assert "api_calls_limit" in free_tier
        assert "predictions_per_day" in free_tier
        assert "features" in free_tier
    
    def test_upgrade_subscription_success(self, client: TestClient, auth_headers: dict):
        """Test successful subscription upgrade."""
        upgrade_data = {
            "new_tier": "basic",
            "payment_method_id": "pm_test_success"
        }
        
        response = client.post("/api/v1/subscriptions/upgrade", json=upgrade_data, headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "message" in data
        assert "new_tier" in data
        assert data["new_tier"] == "basic"
    
    def test_upgrade_subscription_payment_failure(self, client: TestClient, auth_headers: dict):
        """Test subscription upgrade with payment failure."""
        upgrade_data = {
            "new_tier": "basic",
            "payment_method_id": "fail_for_demo"
        }
        
        response = client.post("/api/v1/subscriptions/upgrade", json=upgrade_data, headers=auth_headers)
        assert response.status_code == 402
        assert "Payment failed" in response.json()["detail"]
    
    def test_upgrade_subscription_invalid_tier(self, client: TestClient, premium_auth_headers: dict):
        """Test subscription upgrade to same or lower tier."""
        upgrade_data = {
            "new_tier": "free",  # Downgrade from premium
            "payment_method_id": "pm_test_success"
        }
        
        response = client.post("/api/v1/subscriptions/upgrade", json=upgrade_data, headers=premium_auth_headers)
        assert response.status_code == 400
        assert "Cannot downgrade" in response.json()["detail"]
    
    def test_request_downgrade(self, client: TestClient, premium_auth_headers: dict):
        """Test requesting subscription downgrade."""
        response = client.post("/api/v1/subscriptions/downgrade", headers=premium_auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "message" in data
        assert "support_email" in data
    
    def test_cancel_subscription(self, client: TestClient, premium_auth_headers: dict):
        """Test subscription cancellation."""
        response = client.post("/api/v1/subscriptions/cancel", headers=premium_auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "message" in data
        assert "access_until" in data
    
    def test_get_billing_history(self, client: TestClient, auth_headers: dict):
        """Test getting billing history."""
        response = client.get("/api/v1/subscriptions/billing-history", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "message" in data  # Currently returns placeholder message
    
    def test_subscription_endpoints_require_auth(self, client: TestClient):
        """Test that subscription endpoints require authentication."""
        endpoints = [
            "/api/v1/subscriptions/usage",
            "/api/v1/subscriptions/billing-history",
        ]
        
        for endpoint in endpoints:
            response = client.get(endpoint)
            assert response.status_code == 401


class TestSubscriptionTierBehavior:
    """Test behavior differences between subscription tiers."""
    
    def test_free_tier_limits(self, subscription_service: SubscriptionService):
        """Test FREE tier limits."""
        config = subscription_service.get_tier_config(SubscriptionTier.FREE)
        
        assert config["api_calls_limit"] == 100
        assert config["predictions_per_day"] == 5
        assert config["max_days_ahead"] == 30
        assert config["price"] == 0.0
    
    def test_basic_tier_limits(self, subscription_service: SubscriptionService):
        """Test BASIC tier limits."""
        config = subscription_service.get_tier_config(SubscriptionTier.BASIC)
        
        assert config["api_calls_limit"] == 1000
        assert config["predictions_per_day"] == 50
        assert config["max_days_ahead"] == 60
        assert config["price"] > 0
    
    def test_premium_tier_limits(self, subscription_service: SubscriptionService):
        """Test PREMIUM tier limits."""
        config = subscription_service.get_tier_config(SubscriptionTier.PREMIUM)
        
        assert config["api_calls_limit"] == 10000
        assert config["predictions_per_day"] == 200
        assert config["max_days_ahead"] == 180
        assert config["price"] > 0
    
    def test_enterprise_tier_limits(self, subscription_service: SubscriptionService):
        """Test ENTERPRISE tier limits."""
        config = subscription_service.get_tier_config(SubscriptionTier.ENTERPRISE)
        
        assert config["api_calls_limit"] == 100000
        assert config["predictions_per_day"] == 1000
        assert config["max_days_ahead"] == 365
        assert config["price"] > 0
    
    def test_tier_feature_progression(self, subscription_service: SubscriptionService):
        """Test that higher tiers have more features."""
        free_features = set(subscription_service.get_tier_config(SubscriptionTier.FREE)["features"])
        basic_features = set(subscription_service.get_tier_config(SubscriptionTier.BASIC)["features"])
        premium_features = set(subscription_service.get_tier_config(SubscriptionTier.PREMIUM)["features"])
        enterprise_features = set(subscription_service.get_tier_config(SubscriptionTier.ENTERPRISE)["features"])
        
        # Higher tiers should include all features from lower tiers
        assert free_features.issubset(basic_features)
        assert basic_features.issubset(premium_features)
        assert premium_features.issubset(enterprise_features)


class TestSubscriptionValidation:
    """Test subscription validation logic."""
    
    def test_api_call_validation_different_tiers(self, db_session: Session):
        """Test API call validation across different subscription tiers."""
        subscription_service = SubscriptionService(db_session)
        
        # Create users with different tiers
        free_user = User(email="<EMAIL>", subscription_tier=SubscriptionTier.FREE, api_calls_used=99)
        basic_user = User(email="<EMAIL>", subscription_tier=SubscriptionTier.BASIC, api_calls_used=999)
        
        # Free user at limit should still be allowed (99 < 100)
        assert subscription_service.validate_api_call(free_user) is True
        
        # Basic user at limit should still be allowed (999 < 1000)
        assert subscription_service.validate_api_call(basic_user) is True
        
        # Exceed limits
        free_user.api_calls_used = 100
        basic_user.api_calls_used = 1000
        
        with pytest.raises(Exception):
            subscription_service.validate_api_call(free_user)
        
        with pytest.raises(Exception):
            subscription_service.validate_api_call(basic_user)
    
    def test_prediction_validation_different_tiers(self, db_session: Session):
        """Test prediction validation across different subscription tiers."""
        subscription_service = SubscriptionService(db_session)
        
        free_user = User(email="<EMAIL>", subscription_tier=SubscriptionTier.FREE)
        premium_user = User(email="<EMAIL>", subscription_tier=SubscriptionTier.PREMIUM)
        
        # Free user can predict 30 days ahead
        assert subscription_service.validate_prediction(free_user, days_ahead=30) is True
        
        # Premium user can predict 180 days ahead
        assert subscription_service.validate_prediction(premium_user, days_ahead=180) is True
        
        # Free user cannot predict 60 days ahead
        with pytest.raises(Exception):
            subscription_service.validate_prediction(free_user, days_ahead=60)
    
    def test_feature_access_validation(self, db_session: Session):
        """Test feature access validation across tiers."""
        subscription_service = SubscriptionService(db_session)
        
        free_user = User(email="<EMAIL>", subscription_tier=SubscriptionTier.FREE)
        enterprise_user = User(email="<EMAIL>", subscription_tier=SubscriptionTier.ENTERPRISE)
        
        # Free user has basic features
        assert subscription_service.validate_feature_access(free_user, "basic_predictions") is True
        
        # Enterprise user has all features
        assert subscription_service.validate_feature_access(enterprise_user, "advanced_analytics") is True
        assert subscription_service.validate_feature_access(enterprise_user, "custom_models") is True
        
        # Free user doesn't have advanced features
        with pytest.raises(Exception):
            subscription_service.validate_feature_access(free_user, "advanced_analytics")


class TestSubscriptionUpgradeFlow:
    """Test complete subscription upgrade workflow."""
    
    def test_complete_upgrade_flow(self, client: TestClient, auth_headers: dict, db_session: Session):
        """Test complete subscription upgrade flow."""
        # 1. Check initial tier
        response = client.get("/api/v1/subscriptions/usage", headers=auth_headers)
        assert response.status_code == 200
        initial_data = response.json()
        assert initial_data["subscription_tier"] == "free"
        
        # 2. Get available tiers
        response = client.get("/api/v1/subscriptions/tiers")
        assert response.status_code == 200
        tiers_data = response.json()
        assert "basic" in tiers_data["tiers"]
        
        # 3. Upgrade subscription
        upgrade_data = {
            "new_tier": "basic",
            "payment_method_id": "pm_test_success"
        }
        
        response = client.post("/api/v1/subscriptions/upgrade", json=upgrade_data, headers=auth_headers)
        assert response.status_code == 200
        
        # 4. Verify upgrade
        response = client.get("/api/v1/subscriptions/usage", headers=auth_headers)
        assert response.status_code == 200
        updated_data = response.json()
        assert updated_data["subscription_tier"] == "basic"
        assert updated_data["api_calls_limit"] > initial_data["api_calls_limit"]
