"""
Authentication endpoints
"""

from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import OAuth2PasswordR<PERSON><PERSON>F<PERSON>
from pydantic import BaseModel, EmailStr
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import (
    authenticate_user,
    create_access_token,
    get_password_hash,
    get_current_active_user,
    oauth2_scheme
)
from app.models.user import User, SubscriptionTier

router = APIRouter()


class UserCreate(BaseModel):
    """User registration model"""
    email: EmailStr
    password: str
    full_name: Optional[str] = None


class UserLogin(BaseModel):
    """User login model"""
    email: EmailStr
    password: str


class Token(BaseModel):
    """Token response model"""
    access_token: str
    token_type: str
    expires_in: int


class UserResponse(BaseModel):
    """User response model"""
    id: int
    email: str
    full_name: Optional[str]
    is_active: bool
    subscription_tier: str
    created_at: datetime


@router.post("/register", response_model=UserResponse)
async def register_user(user_data: UserCreate, db: Session = Depends(get_db)):
    """Register a new user"""
    # Check if user already exists
    existing_user = db.query(User).filter(User.email == user_data.email).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Create new user
    hashed_password = get_password_hash(user_data.password)
    db_user = User(
        email=user_data.email,
        hashed_password=hashed_password,
        full_name=user_data.full_name,
        subscription_tier=SubscriptionTier.FREE
    )

    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    return UserResponse(
        id=db_user.id,
        email=db_user.email,
        full_name=db_user.full_name,
        is_active=db_user.is_active,
        subscription_tier=db_user.subscription_tier.value,
        created_at=db_user.created_at
    )


@router.post("/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """Login and get access token"""
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=30)  # 30 minutes
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )

    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_in=1800  # 30 minutes in seconds
    )


@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """Login and get access token (alias for /token)"""
    # Check if user exists and get more specific error messages
    from app.core.auth import get_user_by_email, verify_password

    user = get_user_by_email(db, form_data.username)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is inactive",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # User is valid and active, create token
    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )

    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_in=1800  # 30 minutes in seconds
    )


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user information"""
    return UserResponse(
        id=current_user.id,
        email=current_user.email,
        full_name=current_user.full_name,
        is_active=current_user.is_active,
        subscription_tier=current_user.subscription_tier.value,
        created_at=current_user.created_at
    )


@router.post("/refresh", response_model=Token)
async def refresh_access_token(token: str = Depends(oauth2_scheme)):
    """Refresh access token"""
    # TODO: Implement token refresh
    return Token(
        access_token="new_mock_access_token",
        token_type="bearer",
        expires_in=3600
    )


@router.post("/logout")
async def logout(token: str = Depends(oauth2_scheme)):
    """Logout user"""
    # TODO: Implement logout (blacklist token)
    return {"message": "Successfully logged out"}


@router.post("/forgot-password")
async def forgot_password(email: EmailStr):
    """Send password reset email"""
    # TODO: Implement password reset
    return {"message": "Password reset email sent"}


@router.post("/reset-password")
async def reset_password(token: str, new_password: str):
    """Reset password with token"""
    # TODO: Implement password reset
    return {"message": "Password reset successfully"}
