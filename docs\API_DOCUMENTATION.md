# SAAS ML Trading Bot API Documentation

## Overview

The SAAS ML Trading Bot provides AI-powered stock and cryptocurrency price predictions using LSTM neural networks. The API supports multiple subscription tiers with different usage limits and features.

## Base URL

```
http://localhost:8000/api/v1
```

## Authentication

All protected endpoints require JWT authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

### Getting a Token

**POST** `/auth/login`

```json
{
  "username": "<EMAIL>",
  "password": "your_password"
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

## Subscription Tiers

| Tier | API Calls/Month | Predictions/Day | Max Days Ahead | Price |
|------|----------------|-----------------|----------------|-------|
| FREE | 100 | 5 | 30 | $0 |
| BASIC | 1,000 | 50 | 60 | $9.99 |
| PREMIUM | 10,000 | 200 | 180 | $29.99 |
| ENTERPRISE | 100,000 | 1,000 | 365 | $99.99 |

## Rate Limiting

All endpoints are rate-limited based on subscription tier:

- **FREE**: 100 requests/hour
- **BASIC**: 500 requests/hour  
- **PREMIUM**: 2,000 requests/hour
- **ENTERPRISE**: 10,000 requests/hour

Rate limit headers are included in all responses:
- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when rate limit resets (Unix timestamp)

## Endpoints

### Authentication

#### Register User
**POST** `/auth/register`

```json
{
  "email": "<EMAIL>",
  "password": "secure_password123",
  "full_name": "John Doe"
}
```

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "subscription_tier": "free",
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z"
}
```

#### Login
**POST** `/auth/login`

Form data:
- `username`: Email address
- `password`: User password

#### Get Current User
**GET** `/auth/me`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "subscription_tier": "free",
  "is_active": true,
  "api_calls_used": 25,
  "created_at": "2024-01-01T00:00:00Z"
}
```

### Stock Predictions

#### Predict Stock Price
**POST** `/stocks/{symbol}/predict`

**Headers:** `Authorization: Bearer <token>`

**Path Parameters:**
- `symbol`: Stock symbol (e.g., "AAPL", "GOOGL")

**Request Body:**
```json
{
  "days_ahead": 5,
  "include_volume_analysis": true,
  "include_sentiment_analysis": false
}
```

**Response:**
```json
{
  "symbol": "AAPL",
  "current_price": 150.25,
  "predictions": [
    {
      "date": "2024-01-02",
      "price": 151.0,
      "confidence": 0.85
    },
    {
      "date": "2024-01-03", 
      "price": 152.5,
      "confidence": 0.83
    }
  ],
  "confidence_score": 0.84,
  "model_accuracy": 0.78,
  "created_at": "2024-01-01T12:00:00Z"
}
```

#### Get Stock Historical Data
**GET** `/stocks/{symbol}/history`

**Query Parameters:**
- `period`: Time period (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
- `interval`: Data interval (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo)

### Cryptocurrency Predictions

#### Predict Crypto Price
**POST** `/crypto/{symbol}/predict`

**Headers:** `Authorization: Bearer <token>`

**Path Parameters:**
- `symbol`: Crypto symbol (e.g., "bitcoin", "ethereum")

**Request Body:**
```json
{
  "days_ahead": 7,
  "include_volume_analysis": true,
  "include_market_cap_analysis": true
}
```

**Response:**
```json
{
  "symbol": "bitcoin",
  "current_price": 45000.0,
  "predictions": [
    {
      "date": "2024-01-02",
      "price": 45500.0,
      "confidence": 0.82
    }
  ],
  "confidence_score": 0.80,
  "model_accuracy": 0.75,
  "market_cap": 850000000000,
  "volume_24h": 25000000000,
  "created_at": "2024-01-01T12:00:00Z"
}
```

### Subscription Management

#### Get Usage Statistics
**GET** `/subscriptions/usage`

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "subscription_tier": "free",
  "api_calls_used": 25,
  "api_calls_limit": 100,
  "api_usage_percent": 25.0,
  "daily_predictions_used": 2,
  "daily_predictions_limit": 5,
  "daily_usage_percent": 40.0,
  "max_days_ahead": 30,
  "features": ["basic_predictions", "historical_data"],
  "price": 0.0
}
```

#### Get Subscription Tiers
**GET** `/subscriptions/tiers`

**Response:**
```json
{
  "tiers": {
    "free": {
      "price": 0.0,
      "api_calls_limit": 100,
      "predictions_per_day": 5,
      "max_days_ahead": 30,
      "features": ["basic_predictions", "historical_data"]
    },
    "basic": {
      "price": 9.99,
      "api_calls_limit": 1000,
      "predictions_per_day": 50,
      "max_days_ahead": 60,
      "features": ["basic_predictions", "historical_data", "volume_analysis"]
    }
  }
}
```

#### Upgrade Subscription
**POST** `/subscriptions/upgrade`

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "new_tier": "premium",
  "payment_method_id": "pm_1234567890"
}
```

**Response:**
```json
{
  "message": "Subscription upgraded successfully",
  "new_tier": "premium",
  "effective_date": "2024-01-01T12:00:00Z"
}
```

### Admin Endpoints

#### Get System Statistics
**GET** `/admin/stats`

**Headers:** `Authorization: Bearer <admin_token>`

**Response:**
```json
{
  "total_users": 1250,
  "active_users": 980,
  "total_predictions": 15000,
  "subscription_breakdown": {
    "free": 800,
    "basic": 300,
    "premium": 120,
    "enterprise": 30
  },
  "api_usage_stats": {
    "total_api_calls": 50000,
    "predictions_today": 500,
    "average_daily_usage": 1200
  }
}
```

#### Get All Users
**GET** `/admin/users`

**Query Parameters:**
- `limit`: Number of users to return (default: 100)
- `offset`: Number of users to skip (default: 0)

#### Update User
**PUT** `/admin/users/{user_id}`

**Request Body:**
```json
{
  "subscription_tier": "premium",
  "is_active": true
}
```

## Error Responses

### Standard Error Format

```json
{
  "detail": "Error description",
  "error_code": "ERROR_CODE",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Common HTTP Status Codes

- **200**: Success
- **201**: Created
- **400**: Bad Request - Invalid input data
- **401**: Unauthorized - Missing or invalid authentication
- **403**: Forbidden - Insufficient permissions
- **404**: Not Found - Resource doesn't exist
- **422**: Validation Error - Invalid request format
- **429**: Too Many Requests - Rate limit exceeded
- **500**: Internal Server Error

### Specific Error Codes

- `INVALID_CREDENTIALS`: Login failed
- `USER_ALREADY_EXISTS`: Registration with existing email
- `SUBSCRIPTION_LIMIT_EXCEEDED`: Usage limit reached
- `INVALID_SYMBOL`: Stock/crypto symbol not found
- `INSUFFICIENT_DATA`: Not enough historical data for prediction
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `ADMIN_ACCESS_REQUIRED`: Admin endpoint access denied

## SDKs and Examples

### Python Example

```python
import requests

# Login
login_response = requests.post(
    "http://localhost:8000/api/v1/auth/login",
    data={"username": "<EMAIL>", "password": "password"}
)
token = login_response.json()["access_token"]

# Make prediction
headers = {"Authorization": f"Bearer {token}"}
prediction_response = requests.post(
    "http://localhost:8000/api/v1/stocks/AAPL/predict",
    json={"days_ahead": 5},
    headers=headers
)
prediction = prediction_response.json()
print(f"AAPL prediction: {prediction['predictions']}")
```

### JavaScript Example

```javascript
// Login
const loginResponse = await fetch('/api/v1/auth/login', {
  method: 'POST',
  headers: {'Content-Type': 'application/x-www-form-urlencoded'},
  body: 'username=<EMAIL>&password=password'
});
const { access_token } = await loginResponse.json();

// Make prediction
const predictionResponse = await fetch('/api/v1/stocks/AAPL/predict', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${access_token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ days_ahead: 5 })
});
const prediction = await predictionResponse.json();
console.log('AAPL prediction:', prediction.predictions);
```

## Webhooks

### Subscription Events

Configure webhooks to receive notifications about subscription events:

**POST** `/webhooks/subscription`

Events:
- `subscription.upgraded`
- `subscription.downgraded` 
- `subscription.cancelled`
- `usage.limit_reached`

### Prediction Events

**POST** `/webhooks/prediction`

Events:
- `prediction.completed`
- `prediction.failed`
- `model.retrained`

## Support

For API support, contact: <EMAIL>

Documentation version: 1.0.0
Last updated: 2024-01-01
