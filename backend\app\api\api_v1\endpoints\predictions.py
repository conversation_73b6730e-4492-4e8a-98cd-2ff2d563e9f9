"""
Prediction management endpoints
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.security import <PERSON>A<PERSON>2<PERSON><PERSON>wordBearer
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, date
from enum import Enum
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import get_current_active_user
from app.models.user import User
from app.models import Prediction, AssetType as DBAssetType, PredictionStatus as DBPredictionStatus
from app.services.database_service import DatabaseService

router = APIRouter()

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


class AssetType(str, Enum):
    """Asset type enumeration"""
    STOCK = "stock"
    CRYPTO = "crypto"


class PredictionStatus(str, Enum):
    """Prediction status enumeration"""
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"


class PredictionRecord(BaseModel):
    """Prediction record model"""
    id: int
    asset_type: AssetType
    symbol: str
    prediction_date: datetime
    target_date: date
    predicted_price: float
    actual_price: Optional[float]
    accuracy_score: Optional[float]
    status: PredictionStatus
    model_version: str
    created_at: datetime


class PredictionSummary(BaseModel):
    """Prediction summary model"""
    total_predictions: int
    successful_predictions: int
    average_accuracy: float
    best_performing_asset: str
    worst_performing_asset: str


@router.get("/history", response_model=List[PredictionRecord])
async def get_prediction_history(
    asset_type: Optional[AssetType] = Query(None, description="Filter by asset type"),
    symbol: Optional[str] = Query(None, description="Filter by symbol"),
    limit: int = Query(50, ge=1, le=200, description="Number of records to return"),
    offset: int = Query(0, ge=0, description="Number of records to skip"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get user's prediction history"""
    try:
        db_service = DatabaseService(db)
        predictions = db_service.get_user_predictions(current_user.id, limit + offset)

        # Apply filters
        filtered_predictions = predictions
        if asset_type:
            db_asset_type = DBAssetType.STOCK if asset_type == AssetType.STOCK else DBAssetType.CRYPTO
            filtered_predictions = [p for p in filtered_predictions if p.asset_type == db_asset_type]
        if symbol:
            filtered_predictions = [p for p in filtered_predictions if p.symbol.upper() == symbol.upper()]

        # Apply offset and limit
        paginated_predictions = filtered_predictions[offset:offset + limit]

        # Convert to response format
        result = []
        for p in paginated_predictions:
            # Get the first predicted price as the main prediction
            predicted_price = p.predicted_prices[0] if p.predicted_prices else 0.0

            # Calculate target date (first prediction date)
            target_date = date.today()
            if p.prediction_dates:
                try:
                    target_date = datetime.strptime(p.prediction_dates[0], "%Y-%m-%d").date()
                except:
                    pass

            result.append(PredictionRecord(
                id=p.id,
                asset_type=AssetType.STOCK if p.asset_type == DBAssetType.STOCK else AssetType.CRYPTO,
                symbol=p.symbol,
                prediction_date=p.created_at,
                target_date=target_date,
                predicted_price=predicted_price,
                actual_price=None,  # TODO: Implement actual price tracking
                accuracy_score=p.confidence_score,
                status=PredictionStatus.COMPLETED if p.status == DBPredictionStatus.COMPLETED else PredictionStatus.PENDING,
                model_version=p.model_version,
                created_at=p.created_at
            ))

        return result

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving prediction history: {str(e)}"
        )


@router.get("/summary", response_model=PredictionSummary)
async def get_prediction_summary(
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    token: str = Depends(oauth2_scheme)
):
    """Get prediction accuracy summary"""
    # TODO: Implement actual prediction summary calculation
    return PredictionSummary(
        total_predictions=25,
        successful_predictions=22,
        average_accuracy=0.87,
        best_performing_asset="AAPL",
        worst_performing_asset="DOGE"
    )


@router.get("/{prediction_id}", response_model=PredictionRecord)
async def get_prediction_details(
    prediction_id: int,
    token: str = Depends(oauth2_scheme)
):
    """Get detailed information about a specific prediction"""
    # TODO: Implement actual prediction retrieval
    return PredictionRecord(
        id=prediction_id,
        asset_type=AssetType.STOCK,
        symbol="AAPL",
        prediction_date=datetime.now(),
        target_date=date.today(),
        predicted_price=150.0,
        actual_price=152.5,
        accuracy_score=0.98,
        status=PredictionStatus.COMPLETED,
        model_version="v1.0.0",
        created_at=datetime.now()
    )


@router.delete("/{prediction_id}")
async def delete_prediction(
    prediction_id: int,
    token: str = Depends(oauth2_scheme)
):
    """Delete a prediction record"""
    # TODO: Implement prediction deletion
    return {"message": f"Prediction {prediction_id} deleted successfully"}


@router.get("/compare/{symbol}")
async def compare_predictions(
    symbol: str,
    days: int = Query(30, ge=1, le=90, description="Number of days to compare"),
    token: str = Depends(oauth2_scheme)
):
    """Compare prediction accuracy for a specific symbol over time"""
    # TODO: Implement prediction comparison
    return {
        "symbol": symbol.upper(),
        "comparison_period": f"{days} days",
        "predictions": [],
        "message": "Prediction comparison endpoint - to be implemented"
    }
