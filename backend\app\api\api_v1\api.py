"""
API v1 Router - Main API routing configuration
"""

from fastapi import APIRouter

from app.api.api_v1.endpoints import stocks, crypto, auth, users, predictions, subscriptions, admin

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(stocks.router, prefix="/stocks", tags=["stocks"])
api_router.include_router(crypto.router, prefix="/crypto", tags=["crypto"])
api_router.include_router(predictions.router, prefix="/predictions", tags=["predictions"])
api_router.include_router(subscriptions.router, prefix="/subscriptions", tags=["subscriptions"])
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
