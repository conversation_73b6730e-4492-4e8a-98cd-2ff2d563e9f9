"""
Cryptocurrency Data Preprocessor for LSTM Model Training

This module handles preprocessing of cryptocurrency data including:
- Technical indicators calculation (adapted for crypto volatility)
- Data normalization and denormalization
- Sequence preparation for LSTM training
- Feature engineering for crypto-specific patterns
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


class CryptoDataPreprocessor:
    """
    Preprocessor for cryptocurrency data to prepare it for LSTM model training.
    Handles crypto-specific volatility patterns and technical indicators.
    """
    
    def __init__(self, sequence_length: int = 60, prediction_days: int = 7):
        """
        Initialize the crypto data preprocessor
        
        Args:
            sequence_length: Number of days to look back for prediction
            prediction_days: Number of days to predict ahead
        """
        self.sequence_length = sequence_length
        self.prediction_days = prediction_days
        self.scalers = {}  # Store scalers for each feature
        self.feature_columns = []
        
    def preprocess_crypto_data(self, crypto_data: List[Dict]) -> <PERSON><PERSON>[np.ndarray, pd.DataFrame]:
        """
        Preprocess cryptocurrency data for LSTM training
        
        Args:
            crypto_data: List of crypto data dictionaries with date, price, volume
            
        Returns:
            Tuple of (processed_data_array, dataframe)
        """
        try:
            # Convert to DataFrame
            df = pd.DataFrame(crypto_data)
            
            # Ensure we have the required columns
            if 'date' not in df.columns or 'price' not in df.columns:
                raise ValueError("Crypto data must contain 'date' and 'price' columns")
            
            # Convert date column
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date').reset_index(drop=True)
            
            # Add missing columns with default values if needed
            if 'volume' not in df.columns:
                df['volume'] = df['price'] * 1000000  # Estimate volume
            
            # Create OHLC data from price (crypto APIs often provide only price)
            df['open'] = df['price'].shift(1).fillna(df['price'])
            df['high'] = df[['open', 'price']].max(axis=1) * 1.02  # Add some volatility
            df['low'] = df[['open', 'price']].min(axis=1) * 0.98   # Add some volatility
            df['close'] = df['price']
            
            # Calculate crypto-specific technical indicators
            df = self.calculate_crypto_indicators(df)
            
            # Remove rows with NaN values
            df = df.dropna().reset_index(drop=True)
            
            if len(df) < self.sequence_length:
                raise ValueError(f"Not enough data points. Need at least {self.sequence_length}, got {len(df)}")
            
            # Select features for training
            feature_columns = [
                'close', 'volume', 'volatility', 'price_momentum',
                'SMA_7', 'SMA_14', 'SMA_30', 'EMA_12', 'EMA_26',
                'RSI', 'MACD', 'MACD_signal', 'BB_upper', 'BB_lower', 'BB_width',
                'volume_sma', 'price_change_pct', 'volume_change_pct'
            ]
            
            # Filter to only include columns that exist
            available_columns = [col for col in feature_columns if col in df.columns]
            self.feature_columns = available_columns
            
            # Normalize the data
            processed_data = self.normalize_data(df[available_columns].values, 'crypto_features')
            
            logger.info(f"Preprocessed crypto data: {len(df)} rows, {len(available_columns)} features")
            
            return processed_data, df
            
        except Exception as e:
            logger.error(f"Error preprocessing crypto data: {e}")
            raise
    
    def calculate_crypto_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate cryptocurrency-specific technical indicators
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with additional technical indicators
        """
        try:
            # Basic price indicators
            df['SMA_7'] = df['close'].rolling(window=7).mean()
            df['SMA_14'] = df['close'].rolling(window=14).mean()
            df['SMA_30'] = df['close'].rolling(window=30).mean()
            
            # Exponential Moving Averages
            df['EMA_12'] = df['close'].ewm(span=12).mean()
            df['EMA_26'] = df['close'].ewm(span=26).mean()
            
            # MACD (Moving Average Convergence Divergence)
            df['MACD'] = df['EMA_12'] - df['EMA_26']
            df['MACD_signal'] = df['MACD'].ewm(span=9).mean()
            
            # RSI (Relative Strength Index)
            df['RSI'] = self.calculate_rsi(df['close'], window=14)
            
            # Bollinger Bands
            bb_window = 20
            df['BB_middle'] = df['close'].rolling(window=bb_window).mean()
            bb_std = df['close'].rolling(window=bb_window).std()
            df['BB_upper'] = df['BB_middle'] + (bb_std * 2)
            df['BB_lower'] = df['BB_middle'] - (bb_std * 2)
            df['BB_width'] = (df['BB_upper'] - df['BB_lower']) / df['BB_middle']
            
            # Crypto-specific indicators
            # Volatility (important for crypto)
            df['volatility'] = df['close'].rolling(window=14).std() / df['close'].rolling(window=14).mean()
            
            # Price momentum
            df['price_momentum'] = df['close'].pct_change(periods=7)
            
            # Volume indicators
            df['volume_sma'] = df['volume'].rolling(window=14).mean()
            
            # Price and volume change percentages
            df['price_change_pct'] = df['close'].pct_change()
            df['volume_change_pct'] = df['volume'].pct_change()
            
            # Fear & Greed proxy (based on volatility and momentum)
            df['fear_greed_proxy'] = (
                (df['volatility'].rolling(window=7).mean() * -1) +  # High volatility = fear
                (df['price_momentum'] * 0.5)  # Positive momentum = greed
            )
            
            return df
            
        except Exception as e:
            logger.error(f"Error calculating crypto indicators: {e}")
            return df
    
    def calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """
        Calculate Relative Strength Index (RSI)
        
        Args:
            prices: Price series
            window: RSI calculation window
            
        Returns:
            RSI values
        """
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def normalize_data(self, data: np.ndarray, feature_name: str) -> np.ndarray:
        """
        Normalize data using Min-Max scaling
        
        Args:
            data: Data to normalize
            feature_name: Name of the feature for storing scaler
            
        Returns:
            Normalized data
        """
        from sklearn.preprocessing import MinMaxScaler
        
        if feature_name not in self.scalers:
            self.scalers[feature_name] = MinMaxScaler(feature_range=(0, 1))
            normalized_data = self.scalers[feature_name].fit_transform(data)
        else:
            normalized_data = self.scalers[feature_name].transform(data)
        
        return normalized_data
    
    def denormalize_data(self, normalized_data: np.ndarray, feature_name: str) -> np.ndarray:
        """
        Denormalize data back to original scale
        
        Args:
            normalized_data: Normalized data
            feature_name: Name of the feature for retrieving scaler
            
        Returns:
            Denormalized data
        """
        if feature_name not in self.scalers:
            raise ValueError(f"No scaler found for feature: {feature_name}")
        
        # Handle different input shapes
        if normalized_data.ndim == 1:
            normalized_data = normalized_data.reshape(-1, 1)
        
        denormalized_data = self.scalers[feature_name].inverse_transform(normalized_data)
        
        return denormalized_data.flatten() if denormalized_data.shape[1] == 1 else denormalized_data
    
    def prepare_sequences(self, data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare sequences for LSTM training
        
        Args:
            data: Preprocessed data array
            
        Returns:
            Tuple of (X, y) for training
        """
        X, y = [], []
        
        for i in range(self.sequence_length, len(data) - self.prediction_days + 1):
            # Input sequence
            X.append(data[i - self.sequence_length:i])
            
            # Target (next day's closing price - first column is typically close price)
            y.append(data[i + self.prediction_days - 1, 0])  # Predict close price
        
        return np.array(X), np.array(y)
    
    def get_feature_importance(self) -> Dict[str, float]:
        """
        Get feature importance based on crypto market characteristics
        
        Returns:
            Dictionary of feature names and their importance scores
        """
        # Crypto-specific feature importance (based on market behavior)
        importance_map = {
            'close': 1.0,
            'volume': 0.8,
            'volatility': 0.9,  # Very important for crypto
            'price_momentum': 0.85,
            'RSI': 0.7,
            'MACD': 0.75,
            'BB_width': 0.6,
            'fear_greed_proxy': 0.65,
            'SMA_7': 0.5,
            'SMA_14': 0.55,
            'SMA_30': 0.6,
            'EMA_12': 0.5,
            'EMA_26': 0.55
        }
        
        # Return only for features that are actually used
        return {feature: importance_map.get(feature, 0.3) 
                for feature in self.feature_columns}
