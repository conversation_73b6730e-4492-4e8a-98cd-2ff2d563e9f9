#!/usr/bin/env python3
"""Test script to show REAL vs DUMMY data comparison"""

from app.services.stock_service import StockService
from app.services.crypto_service import CryptoService
import asyncio
import yfinance as yf

async def test_real_vs_dummy():
    stock_service = StockService()

    print('=== TESTING REAL vs DUMMY DATA ===')
    print()

    # Test PyTorch availability
    print('1. PYTORCH AVAILABILITY CHECK:')
    try:
        from app.services.stock_service import PYTORCH_AVAILABLE
        print(f'PyTorch Available: {PYTORCH_AVAILABLE}')
        if PYTORCH_AVAILABLE:
            print('✅ Real LSTM predictions ENABLED!')
        else:
            print('❌ Using fallback predictions only')
    except Exception as e:
        print(f'Error checking PyTorch: {e}')
    print()

    # Test real market data fetching
    print('2. REAL MARKET DATA TEST:')
    try:
        ticker = yf.Ticker('AAPL')
        data = ticker.history(period='5d')
        if not data.empty:
            current_price = data['Close'].iloc[-1]
            print(f'✅ Real AAPL current price: ${current_price:.2f}')
            print(f'✅ Data points available: {len(data)} days')
            print(f'✅ Price range: ${data["Close"].min():.2f} - ${data["Close"].max():.2f}')
        else:
            print('❌ No real market data available')
    except Exception as e:
        print(f'❌ Error fetching real data: {e}')
    print()

    # Test stock prediction with real implementation
    print('3. REAL STOCK PREDICTION TEST:')
    try:
        result = await stock_service.predict_stock_price('AAPL', 3)
        print(f'Symbol: {result["symbol"]}')
        print(f'Current Price: ${result["current_price"]:.2f}')
        print(f'Predicted Prices: {[f"${p:.2f}" for p in result["predicted_prices"]]}')
        print(f'Model Type: {result.get("model_type", "Unknown")}')
        print(f'Model Version: {result["model_version"]}')
        print(f'Confidence Score: {result["confidence_score"]:.2f}')
        print(f'Note: {result.get("note", "N/A")}')

        # Check if it's real or fallback
        if 'PYTORCH' in result.get("model_type", ""):
            print('✅ This is a REAL PyTorch LSTM prediction!')
        elif 'trend' in result.get("model_version", "").lower():
            print('⚠️  This is real trend analysis (fallback)')
        else:
            print('❌ This might be dummy data')
        print()
    except Exception as e:
        print(f'❌ Error: {e}')
        print()

    # Test multiple predictions to check consistency
    print('4. CONSISTENCY TEST (Real predictions should be similar):')
    try:
        result1 = await stock_service.predict_stock_price('AAPL', 3)
        result2 = await stock_service.predict_stock_price('AAPL', 3)

        print(f'Prediction 1: {[f"${p:.2f}" for p in result1["predicted_prices"]]}')
        print(f'Prediction 2: {[f"${p:.2f}" for p in result2["predicted_prices"]]}')

        # Calculate difference
        diff = sum(abs(p1 - p2) for p1, p2 in zip(result1["predicted_prices"], result2["predicted_prices"]))
        print(f'Total difference: ${diff:.2f}')

        if diff < 1.0:  # Very small difference indicates real model
            print('✅ Predictions are consistent - likely REAL model')
        elif diff < 10.0:  # Moderate difference indicates trend analysis
            print('⚠️  Moderate variation - likely trend analysis')
        else:  # Large difference indicates random data
            print('❌ Large variation - likely RANDOM/DUMMY data')

    except Exception as e:
        print(f'❌ Error: {e}')

if __name__ == "__main__":
    asyncio.run(test_real_vs_dummy())
