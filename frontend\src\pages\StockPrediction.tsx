import React, { useState } from "react";
import {
  Container,
  Grid,
  Paper,
  Typography,
  TextField,
  Button,
  Box,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Alert,
  FormControlLabel,
  Switch,
  Autocomplete,
} from "@mui/material";
import { TrendingUp, Search, Timeline, Assessment } from "@mui/icons-material";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

const StockPrediction: React.FC = () => {
  const [selectedStock, setSelectedStock] = useState<string>("");
  const [daysAhead, setDaysAhead] = useState<number>(30);
  const [includeTechnicalIndicators, setIncludeTechnicalIndicators] =
    useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);
  const [prediction, setPrediction] = useState<any>(null);
  const [error, setError] = useState<string>("");

  // Mock stock options
  const stockOptions = [
    { label: "Apple Inc. (AAPL)", value: "AAPL" },
    { label: "Microsoft Corporation (MSFT)", value: "MSFT" },
    { label: "Tesla, Inc. (TSLA)", value: "TSLA" },
    { label: "Amazon.com, Inc. (AMZN)", value: "AMZN" },
    { label: "Alphabet Inc. (GOOGL)", value: "GOOGL" },
    { label: "Meta Platforms, Inc. (META)", value: "META" },
    { label: "NVIDIA Corporation (NVDA)", value: "NVDA" },
  ];

  // Mock prediction data
  const mockPredictionData = [
    { date: "2024-01-01", actual: 150, predicted: 151 },
    { date: "2024-01-02", actual: 152, predicted: 153 },
    { date: "2024-01-03", actual: 148, predicted: 149 },
    { date: "2024-01-04", actual: 155, predicted: 154 },
    { date: "2024-01-05", actual: 157, predicted: 158 },
    { date: "2024-01-06", actual: null, predicted: 160 },
    { date: "2024-01-07", actual: null, predicted: 162 },
    { date: "2024-01-08", actual: null, predicted: 159 },
  ];

  const handlePredict = async () => {
    if (!selectedStock) {
      setError("Please select a stock symbol");
      return;
    }

    setLoading(true);
    setError("");

    try {
      // Real API call to backend
      const token = localStorage.getItem("token");
      const response = await fetch(
        `http://localhost:8000/api/v1/stocks/${selectedStock}/predict`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            days_ahead: daysAhead,
          }),
        }
      );

      if (!response.ok) {
        if (response.status === 401) {
          setError("Please log in to make predictions");
          return;
        }
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();

      // Convert API response to frontend format
      const predictionData = data.predicted_prices.map(
        (price: number, index: number) => ({
          date: new Date(Date.now() + (index + 1) * 24 * 60 * 60 * 1000)
            .toISOString()
            .split("T")[0],
          actual: null,
          predicted: price,
        })
      );

      setPrediction({
        symbol: data.symbol,
        currentPrice: data.current_price,
        predictedPrices: data.predicted_prices,
        confidenceScore: data.confidence_score,
        modelVersion: data.model_version,
        data: predictionData,
        note: data.note,
        modelType: data.model_type,
      });
    } catch (err) {
      console.error("Prediction error:", err);
      setError("Failed to generate prediction. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Stock Price Prediction
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          AI-powered LSTM predictions for stock market analysis
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Prediction Form */}
        <Grid item xs={12} md={4}>
          <Paper
            sx={{
              p: 3,
              background: "linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)",
            }}
          >
            <Typography variant="h6" gutterBottom>
              Generate Prediction
            </Typography>

            <Box sx={{ mt: 3 }}>
              <Autocomplete
                options={stockOptions}
                getOptionLabel={(option) => option.label}
                onChange={(_, value) => setSelectedStock(value?.value || "")}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select Stock"
                    variant="outlined"
                    fullWidth
                    InputProps={{
                      ...params.InputProps,
                      startAdornment: (
                        <Search sx={{ mr: 1, color: "text.secondary" }} />
                      ),
                    }}
                  />
                )}
                sx={{ mb: 3 }}
              />

              <TextField
                label="Days Ahead"
                type="number"
                value={daysAhead}
                onChange={(e) => setDaysAhead(Number(e.target.value))}
                fullWidth
                inputProps={{ min: 1, max: 90 }}
                sx={{ mb: 3 }}
              />

              <FormControlLabel
                control={
                  <Switch
                    checked={includeTechnicalIndicators}
                    onChange={(e) =>
                      setIncludeTechnicalIndicators(e.target.checked)
                    }
                  />
                }
                label="Include Technical Indicators"
                sx={{ mb: 3 }}
              />

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              <Button
                variant="contained"
                fullWidth
                size="large"
                onClick={handlePredict}
                disabled={loading}
                startIcon={
                  loading ? <CircularProgress size={20} /> : <Timeline />
                }
              >
                {loading ? "Generating Prediction..." : "Predict Price"}
              </Button>
            </Box>
          </Paper>

          {/* Current Stock Info */}
          {selectedStock && (
            <Paper
              sx={{
                p: 3,
                mt: 3,
                background: "linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)",
              }}
            >
              <Typography variant="h6" gutterBottom>
                Current Stock Info
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="h4" color="primary">
                  $150.25
                </Typography>
                <Box
                  sx={{ display: "flex", alignItems: "center", gap: 1, mt: 1 }}
                >
                  <TrendingUp sx={{ color: "success.main" }} />
                  <Typography color="success.main">+2.5% (+$3.75)</Typography>
                </Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ mt: 1 }}
                >
                  Volume: 1,234,567 | Market Cap: $2.4T
                </Typography>
              </Box>
            </Paper>
          )}
        </Grid>

        {/* Prediction Results */}
        <Grid item xs={12} md={8}>
          {prediction ? (
            <Paper
              sx={{
                p: 3,
                background: "linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)",
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  mb: 3,
                }}
              >
                <Typography variant="h6">
                  Prediction Results for {prediction.symbol}
                </Typography>
                <Chip
                  label={`${(prediction.confidenceScore * 100).toFixed(
                    1
                  )}% Confidence`}
                  color="success"
                  icon={<Assessment />}
                />
              </Box>

              {/* Chart */}
              <Box sx={{ height: 400, mb: 3 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={prediction.data}>
                    <CartesianGrid
                      strokeDasharray="3 3"
                      stroke="rgba(255,255,255,0.1)"
                    />
                    <XAxis dataKey="date" stroke="#ffffff" />
                    <YAxis stroke="#ffffff" />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: "#1a1d3a",
                        border: "1px solid #2a2d5a",
                        borderRadius: "8px",
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="actual"
                      stroke="#4caf50"
                      strokeWidth={2}
                      name="Actual Price"
                      connectNulls={false}
                    />
                    <Line
                      type="monotone"
                      dataKey="predicted"
                      stroke="#1976d2"
                      strokeWidth={2}
                      strokeDasharray="5 5"
                      name="Predicted Price"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>

              {/* Prediction Summary */}
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Card sx={{ background: "rgba(25, 118, 210, 0.1)" }}>
                    <CardContent>
                      <Typography color="primary" gutterBottom>
                        Current Price
                      </Typography>
                      <Typography variant="h6">
                        ${prediction.currentPrice}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card sx={{ background: "rgba(76, 175, 80, 0.1)" }}>
                    <CardContent>
                      <Typography color="success.main" gutterBottom>
                        30-Day Target
                      </Typography>
                      <Typography variant="h6">
                        $
                        {
                          prediction.predictedPrices[
                            prediction.predictedPrices.length - 1
                          ]
                        }
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card sx={{ background: "rgba(255, 152, 0, 0.1)" }}>
                    <CardContent>
                      <Typography color="warning.main" gutterBottom>
                        Expected Change
                      </Typography>
                      <Typography variant="h6">+6.5%</Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card sx={{ background: "rgba(156, 39, 176, 0.1)" }}>
                    <CardContent>
                      <Typography color="secondary.main" gutterBottom>
                        Model Version
                      </Typography>
                      <Typography variant="h6">
                        {prediction.modelVersion}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Paper>
          ) : (
            <Paper
              sx={{
                p: 6,
                textAlign: "center",
                background: "linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)",
              }}
            >
              <Timeline sx={{ fontSize: 64, color: "text.secondary", mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No Prediction Generated
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Select a stock symbol and click "Predict Price" to generate
                AI-powered predictions
              </Typography>
            </Paper>
          )}
        </Grid>
      </Grid>
    </Container>
  );
};

export default StockPrediction;
