"""
ML Model database models for storing model metadata and training history
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Enum, JSON, Boolean
from sqlalchemy.sql import func
import enum

from app.core.database import Base


class ModelType(enum.Enum):
    STOCK_LSTM = "stock_lstm"
    CRYPTO_LSTM = "crypto_lstm"


class ModelStatus(enum.Enum):
    TRAINING = "training"
    TRAINED = "trained"
    FAILED = "failed"
    DEPRECATED = "deprecated"


class MLModel(Base):
    __tablename__ = "ml_models"

    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String, nullable=False, index=True)
    model_type = Column(Enum(ModelType), nullable=False)
    model_version = Column(String, nullable=False)
    status = Column(Enum(ModelStatus), default=ModelStatus.TRAINING)
    
    # Model parameters
    hyperparameters = Column(JSON, nullable=True)  # Store model hyperparameters
    training_config = Column(JSON, nullable=True)  # Store training configuration
    
    # Performance metrics
    training_loss = Column(Float, nullable=True)
    validation_loss = Column(Float, nullable=True)
    accuracy_score = Column(Float, nullable=True)
    
    # Model file information
    model_file_path = Column(String, nullable=True)
    model_size_bytes = Column(Integer, nullable=True)
    
    # Training information
    training_data_size = Column(Integer, nullable=True)
    training_duration_seconds = Column(Float, nullable=True)
    epochs_completed = Column(Integer, nullable=True)
    
    # Metadata
    is_active = Column(Boolean, default=True)
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_trained_at = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return f"<MLModel(symbol='{self.symbol}', type='{self.model_type.value}', version='{self.model_version}')>"


class TrainingHistory(Base):
    __tablename__ = "training_history"

    id = Column(Integer, primary_key=True, index=True)
    model_id = Column(Integer, nullable=False, index=True)  # Reference to MLModel
    symbol = Column(String, nullable=False, index=True)
    model_type = Column(Enum(ModelType), nullable=False)
    
    # Training session details
    training_start = Column(DateTime(timezone=True), nullable=False)
    training_end = Column(DateTime(timezone=True), nullable=True)
    training_duration_seconds = Column(Float, nullable=True)
    
    # Training data
    data_start_date = Column(DateTime, nullable=True)
    data_end_date = Column(DateTime, nullable=True)
    training_samples = Column(Integer, nullable=True)
    validation_samples = Column(Integer, nullable=True)
    
    # Training results
    final_training_loss = Column(Float, nullable=True)
    final_validation_loss = Column(Float, nullable=True)
    best_epoch = Column(Integer, nullable=True)
    total_epochs = Column(Integer, nullable=True)
    
    # Training configuration
    hyperparameters = Column(JSON, nullable=True)
    training_config = Column(JSON, nullable=True)
    
    # Status and metadata
    status = Column(Enum(ModelStatus), nullable=False)
    error_message = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self):
        return f"<TrainingHistory(model_id={self.model_id}, symbol='{self.symbol}', status='{self.status.value}')>"


class PredictionCache(Base):
    __tablename__ = "prediction_cache"

    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String, nullable=False, index=True)
    model_type = Column(Enum(ModelType), nullable=False)
    model_version = Column(String, nullable=False)
    
    # Cache key and data
    cache_key = Column(String, nullable=False, unique=True, index=True)
    prediction_data = Column(JSON, nullable=False)  # Cached prediction result
    
    # Cache metadata
    days_ahead = Column(Integer, nullable=False)
    confidence_score = Column(Float, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=False)
    
    # Usage tracking
    access_count = Column(Integer, default=0)
    last_accessed = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return f"<PredictionCache(symbol='{self.symbol}', model_type='{self.model_type.value}', expires_at='{self.expires_at}')>"
