"""
Test API endpoints
"""

import requests
import json
import time

def test_api():
    """Test the API endpoints"""
    base_url = "http://127.0.0.1:8001"
    
    print("Testing TradingBot API")
    print("=" * 40)
    
    try:
        # Test 1: Health check
        print("1. Testing health endpoint...")
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print(f"   ✓ Health check passed: {response.json()}")
        else:
            print(f"   ❌ Health check failed: {response.status_code}")
            return False
        
        # Test 2: Stock data
        print("\n2. Testing stock data endpoint...")
        response = requests.get(f"{base_url}/api/v1/stocks/AAPL/data", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ Stock data retrieved for {data.get('symbol', 'UNKNOWN')}")
            print(f"   Current price: ${data.get('current_price', 0):.2f}")
        else:
            print(f"   ❌ Stock data failed: {response.status_code}")
        
        # Test 3: Stock prediction
        print("\n3. Testing stock prediction endpoint...")
        prediction_data = {
            "days_ahead": 5,
            "include_technical_indicators": True
        }
        
        response = requests.post(
            f"{base_url}/api/v1/stocks/AAPL/predict",
            json=prediction_data,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ Prediction generated for {data.get('symbol', 'UNKNOWN')}")
            print(f"   Current price: ${data.get('current_price', 0):.2f}")
            print(f"   Predicted prices: {[f'${p:.2f}' for p in data.get('predicted_prices', [])[:3]]}...")
            print(f"   Confidence score: {data.get('confidence_score', 0):.2f}")
            print(f"   Model version: {data.get('model_version', 'unknown')}")
        else:
            print(f"   ❌ Prediction failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error details: {error_data}")
            except:
                print(f"   Error text: {response.text}")
        
        # Test 4: Model training
        print("\n4. Testing model training endpoint...")
        response = requests.post(
            f"{base_url}/api/v1/stocks/AAPL/train",
            json={"force_retrain": False},
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ Training completed: {data.get('status', 'unknown')}")
        else:
            print(f"   ⚠ Training endpoint: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Details: {error_data}")
            except:
                print(f"   Error text: {response.text}")
        
        # Test 5: Model info
        print("\n5. Testing model info endpoint...")
        response = requests.get(f"{base_url}/api/v1/stocks/AAPL/model-info", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ Model info retrieved")
            print(f"   Model exists: {data.get('model_exists', False)}")
        else:
            print(f"   ⚠ Model info endpoint: {response.status_code}")
        
        print("\n" + "=" * 40)
        print("✅ API testing completed!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to API server.")
        print("Make sure the server is running on http://127.0.0.1:8001")
        return False
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_api()
    if not success:
        print("\n💡 To start the server, run:")
        print("   cd backend/app")
        print("   python -m uvicorn simple_main:app --host 127.0.0.1 --port 8001 --reload")
