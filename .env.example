# TradingBot SAAS ML Platform - Environment Variables

# Security
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=10080

# Database
POSTGRES_SERVER=localhost
POSTGRES_USER=tradingbot
POSTGRES_PASSWORD=your-db-password
POSTGRES_DB=tradingbot_db
POSTGRES_PORT=5432

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# External APIs
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-key
COINGECKO_API_KEY=your-coingecko-key

# CORS Origins (comma-separated)
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:8000

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
PREMIUM_RATE_LIMIT_PER_MINUTE=300

# Model Settings
MODEL_CACHE_TTL=3600
PREDICTION_CACHE_TTL=300

# File Storage
UPLOAD_DIR=uploads
MODEL_DIR=models
DATA_DIR=data
