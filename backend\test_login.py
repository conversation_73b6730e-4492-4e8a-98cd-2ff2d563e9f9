#!/usr/bin/env python3
"""Test login functionality"""

import requests

def test_login():
    """Test login with the new test user"""
    try:
        response = requests.post('http://localhost:8000/api/v1/auth/login', 
            data={
                'username': '<EMAIL>',
                'password': 'test123'
            })
        
        if response.status_code == 200:
            data = response.json()
            print('✅ Login successful!')
            print(f'   Token: {data["access_token"][:50]}...')
            print(f'   Type: {data["token_type"]}')
            
            # Test making a prediction with the token
            print('\n🔮 Testing prediction with token...')
            pred_response = requests.post(
                'http://localhost:8000/api/v1/stocks/AAPL/predict',
                json={'symbol': 'AAPL', 'days_ahead': 3, 'include_technical_indicators': True},
                headers={'Authorization': f'Bearer {data["access_token"]}'},
                timeout=60
            )
            
            if pred_response.status_code == 200:
                pred_data = pred_response.json()
                print('✅ Prediction successful!')
                print(f'   Symbol: {pred_data["symbol"]}')
                print(f'   Current Price: ${pred_data["current_price"]:.2f}')
                print(f'   Predictions: {[f"${p:.2f}" for p in pred_data["predicted_prices"]]}')
                print(f'   Model: {pred_data["model_version"]}')
                print(f'   Type: {pred_data.get("model_type", "Unknown")}')
            else:
                print(f'❌ Prediction failed: {pred_response.status_code}')
                print(f'   Response: {pred_response.text}')
                
        else:
            print(f'❌ Login failed: {response.status_code}')
            print(f'   Response: {response.text}')
            
    except Exception as e:
        print(f'❌ Error: {e}')

if __name__ == "__main__":
    test_login()
