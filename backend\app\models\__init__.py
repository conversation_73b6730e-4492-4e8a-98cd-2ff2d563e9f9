"""
Database models package
"""

from app.core.database import Base
from app.models.user import User, SubscriptionTier
from app.models.prediction import Prediction, PredictionAccuracy, AssetType, PredictionStatus
from app.models.ml_model import MLModel, TrainingHistory, PredictionCache, ModelType, ModelStatus

# Import all models to ensure they are registered with SQLAlchemy
__all__ = [
    "Base",
    "User",
    "SubscriptionTier",
    "Prediction",
    "PredictionAccuracy",
    "AssetType",
    "PredictionStatus",
    "MLModel",
    "TrainingHistory",
    "PredictionCache",
    "ModelType",
    "ModelStatus"
]
