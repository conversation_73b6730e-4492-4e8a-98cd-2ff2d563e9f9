"""
Cryptocurrency prediction endpoints
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query, Request, Response
from pydantic import BaseModel
from datetime import datetime, date
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import get_current_active_user
from app.core.rate_limiter import rate_limit_predictions, subscription_check, add_rate_limit_headers
from app.models.user import User
from app.services.crypto_service import crypto_service

router = APIRouter()


class CryptoPredictionRequest(BaseModel):
    """Crypto prediction request model"""
    symbol: str
    days_ahead: int = 30
    include_volume_analysis: bool = True
    include_sentiment_analysis: bool = False


class CryptoPredictionResponse(BaseModel):
    """Crypto prediction response model"""
    symbol: str
    current_price: float
    predicted_prices: List[float]
    prediction_dates: List[date]
    confidence_score: float
    volatility_score: float
    model_version: str
    created_at: datetime


class CryptoDataResponse(BaseModel):
    """Crypto data response model"""
    symbol: str
    name: str
    current_price: float
    change_percent_24h: float
    volume_24h: float
    market_cap: Optional[float]
    market_cap_rank: Optional[int]
    last_updated: datetime


@router.get("/search", response_model=List[dict])
async def search_crypto(
    query: str = Query(..., min_length=1, description="Crypto symbol or name"),
    limit: int = Query(10, ge=1, le=50, description="Number of results to return")
):
    """Search for cryptocurrencies by symbol or name"""
    from app.services.crypto_service import crypto_service
    return await crypto_service.search_cryptocurrencies(query, limit)


@router.get("/{symbol}/data", response_model=CryptoDataResponse)
async def get_crypto_data(symbol: str):
    """Get current cryptocurrency data"""
    from app.services.crypto_service import crypto_service

    data = await crypto_service.get_crypto_data(symbol)
    if not data:
        raise HTTPException(status_code=404, detail=f"Cryptocurrency {symbol} not found")

    return CryptoDataResponse(
        symbol=data["symbol"],
        name=data["name"],
        current_price=data["current_price"],
        change_percent_24h=data["change_percent"],
        volume_24h=data["volume_24h"],
        market_cap=data.get("market_cap"),
        market_cap_rank=None,  # Not provided by current service
        last_updated=data["last_updated"]
    )


@router.post("/{symbol}/predict", response_model=CryptoPredictionResponse)
async def predict_crypto_price(
    symbol: str,
    request: CryptoPredictionRequest,
    http_request: Request,
    response: Response,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    _rate_limit: bool = Depends(rate_limit_predictions)
):
    """Generate cryptocurrency price predictions using LSTM model"""
    from app.services.crypto_service import crypto_service
    from datetime import timedelta

    # Validate input parameters
    if not symbol or len(symbol.strip()) == 0:
        raise HTTPException(status_code=422, detail="Symbol cannot be empty")

    if request.days_ahead <= 0 or request.days_ahead > 365:
        raise HTTPException(status_code=422, detail="days_ahead must be between 1 and 365")

    symbol = symbol.lower().strip()

    # Validate subscription access with actual request parameters
    from app.services.subscription_service import SubscriptionService
    subscription_service = SubscriptionService(db)
    subscription_service.validate_subscription_access(current_user, "prediction", days_ahead=request.days_ahead)

    try:
        # Add rate limit headers to response
        rate_limit_headers = add_rate_limit_headers(http_request, current_user)
        for header, value in rate_limit_headers.items():
            response.headers[header] = value

        # Use LSTM prediction service with database integration
        prediction_result = await crypto_service.predict_crypto_price(
            symbol=symbol,
            days_ahead=request.days_ahead,
            user_id=current_user.id,
            db=db
        )

        # Convert string dates to date objects
        prediction_dates = [
            datetime.strptime(date_str, "%Y-%m-%d").date()
            for date_str in prediction_result["prediction_dates"]
        ]

        return CryptoPredictionResponse(
            symbol=prediction_result["symbol"],
            current_price=prediction_result["current_price"],
            predicted_prices=prediction_result["predicted_prices"],
            prediction_dates=prediction_dates,
            confidence_score=prediction_result["confidence_score"],
            volatility_score=prediction_result["volatility_score"],
            model_version=prediction_result["model_version"],
            created_at=prediction_result["created_at"]
        )

    except ValueError as e:
        error_msg = str(e).lower()
        if "not found" in error_msg or "invalid symbol" in error_msg or "unknown" in error_msg:
            raise HTTPException(status_code=404, detail=f"Cryptocurrency symbol '{symbol}' not found")
        elif "insufficient data" in error_msg or "minimum" in error_msg:
            raise HTTPException(status_code=400, detail=f"Insufficient data for symbol '{symbol}' to generate prediction")
        else:
            raise HTTPException(status_code=422, detail=f"Invalid input: {str(e)}")
    except Exception as e:
        error_msg = str(e).lower()
        if "not found" in error_msg or "unknown" in error_msg:
            raise HTTPException(status_code=404, detail=f"Cryptocurrency symbol '{symbol}' not found")
        raise HTTPException(status_code=500, detail=f"Error generating prediction: {str(e)}")


@router.get("/{symbol}/history")
async def get_crypto_history(
    symbol: str,
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    interval: str = Query("1d", description="Data interval (1d, 1h, etc.)")
):
    """Get historical cryptocurrency data"""
    from app.services.crypto_service import crypto_service

    start_datetime = datetime.combine(start_date, datetime.min.time()) if start_date else None
    end_datetime = datetime.combine(end_date, datetime.min.time()) if end_date else None

    return await crypto_service.get_historical_data(symbol, start_datetime, end_datetime, interval)


@router.post("/{symbol}/train")
async def train_crypto_model(symbol: str):
    """Train LSTM model for cryptocurrency prediction"""
    from app.ml.crypto.predictor import crypto_predictor

    try:
        result = await crypto_predictor.train_model(symbol, force_retrain=True)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error training model: {str(e)}")


@router.get("/{symbol}/model-info")
async def get_crypto_model_info(symbol: str):
    """Get information about the LSTM model for a cryptocurrency"""
    from app.ml.crypto.predictor import crypto_predictor

    try:
        return crypto_predictor.get_model_info(symbol)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting model info: {str(e)}")


@router.get("/trending")
async def get_trending_crypto(limit: int = Query(10, ge=1, le=50)):
    """Get trending cryptocurrencies"""
    # TODO: Implement trending crypto functionality
    return {
        "trending": [
            {"symbol": "BTC", "name": "Bitcoin", "change_24h": 3.2},
            {"symbol": "ETH", "name": "Ethereum", "change_24h": 5.1},
        ],
        "message": "Trending crypto endpoint - to be implemented"
    }
