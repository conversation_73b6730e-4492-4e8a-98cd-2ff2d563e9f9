"""
Rate limiting middleware and utilities
"""

import time
from typing import Dict, Optional
from collections import defaultdict, deque
from datetime import datetime, timedelta
from fastapi import HTTPException, status, Request, Depends
from sqlalchemy.orm import Session
import logging

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User, SubscriptionTier
from app.services.subscription_service import SubscriptionService

logger = logging.getLogger(__name__)


class RateLimiter:
    """In-memory rate limiter with sliding window algorithm"""
    
    def __init__(self):
        # Store request timestamps for each user/IP
        self.requests: Dict[str, deque] = defaultdict(deque)
        self.cleanup_interval = 300  # Clean up old entries every 5 minutes
        self.last_cleanup = time.time()
    
    def _cleanup_old_entries(self):
        """Remove old entries to prevent memory leaks"""
        current_time = time.time()
        if current_time - self.last_cleanup < self.cleanup_interval:
            return
        
        cutoff_time = current_time - 3600  # Keep last hour of data
        for key in list(self.requests.keys()):
            # Remove old timestamps
            while self.requests[key] and self.requests[key][0] < cutoff_time:
                self.requests[key].popleft()
            
            # Remove empty entries
            if not self.requests[key]:
                del self.requests[key]
        
        self.last_cleanup = current_time
    
    def is_allowed(self, identifier: str, limit: int, window_seconds: int = 3600) -> bool:
        """
        Check if request is allowed based on rate limit
        
        Args:
            identifier: Unique identifier (user_id, IP, etc.)
            limit: Maximum requests allowed in window
            window_seconds: Time window in seconds (default: 1 hour)
        
        Returns:
            True if request is allowed, False otherwise
        """
        self._cleanup_old_entries()
        
        current_time = time.time()
        window_start = current_time - window_seconds
        
        # Get request queue for this identifier
        request_queue = self.requests[identifier]
        
        # Remove old requests outside the window
        while request_queue and request_queue[0] < window_start:
            request_queue.popleft()
        
        # Check if limit is exceeded
        if len(request_queue) >= limit:
            return False
        
        # Add current request
        request_queue.append(current_time)
        return True
    
    def get_remaining_requests(self, identifier: str, limit: int, window_seconds: int = 3600) -> int:
        """Get number of remaining requests in current window"""
        current_time = time.time()
        window_start = current_time - window_seconds
        
        request_queue = self.requests[identifier]
        
        # Count requests in current window
        current_requests = sum(1 for timestamp in request_queue if timestamp >= window_start)
        return max(0, limit - current_requests)
    
    def get_reset_time(self, identifier: str, window_seconds: int = 3600) -> Optional[datetime]:
        """Get when the rate limit will reset for this identifier"""
        request_queue = self.requests[identifier]
        if not request_queue:
            return None

        # The oldest request will expire first
        oldest_request = request_queue[0]
        reset_time = datetime.fromtimestamp(oldest_request + window_seconds)
        return reset_time

    def get_rate_limit_info(self, identifier: str, limit: int, window_seconds: int = 3600) -> Dict:
        """Get comprehensive rate limit information for an identifier"""
        remaining = self.get_remaining_requests(identifier, limit, window_seconds)
        reset_time = self.get_reset_time(identifier, window_seconds)

        return {
            "limit": limit,
            "remaining": remaining,
            "used": limit - remaining,
            "reset_time": reset_time.isoformat() if reset_time else None,
            "window_seconds": window_seconds
        }


# Global rate limiter instance
rate_limiter = RateLimiter()


class RateLimitConfig:
    """Rate limit configurations for different endpoints and tiers"""

    # Check if we're in test mode
    import os
    import sys
    IS_TESTING = (
        os.getenv("TESTING", "false").lower() == "true" or
        "pytest" in os.getenv("_", "") or
        "pytest" in sys.modules or
        any("pytest" in arg for arg in sys.argv)
    )

    # General API rate limits (requests per hour)
    GENERAL_LIMITS = {
        SubscriptionTier.FREE: 1000 if IS_TESTING else 50,
        SubscriptionTier.BASIC: 2000 if IS_TESTING else 200,
        SubscriptionTier.PREMIUM: 5000 if IS_TESTING else 1000,
        SubscriptionTier.ENTERPRISE: 10000 if IS_TESTING else 5000
    }

    # Prediction endpoint limits (requests per hour)
    PREDICTION_LIMITS = {
        SubscriptionTier.FREE: 500 if IS_TESTING else 10,
        SubscriptionTier.BASIC: 1000 if IS_TESTING else 50,
        SubscriptionTier.PREMIUM: 2000 if IS_TESTING else 200,
        SubscriptionTier.ENTERPRISE: 5000 if IS_TESTING else 1000
    }

    # Authentication endpoint limits (requests per hour) - stricter for security
    AUTH_LIMITS = {
        "login": 100 if IS_TESTING else 20,
        "register": 50 if IS_TESTING else 10,
        "password_reset": 25 if IS_TESTING else 5
    }


def check_rate_limit(
    request: Request,
    limit_type: str = "general",
    user: Optional[User] = None,
    db: Optional[Session] = None
) -> None:
    """
    Check rate limit for a request
    
    Args:
        request: FastAPI request object
        limit_type: Type of rate limit ('general', 'prediction', 'auth')
        user: Authenticated user (if available)
        db: Database session (if available)
    """
    client_ip = request.client.host
    
    # Determine identifier and limits
    if user:
        identifier = f"user_{user.id}"
        if limit_type == "general":
            limit = RateLimitConfig.GENERAL_LIMITS[user.subscription_tier]
        elif limit_type == "prediction":
            limit = RateLimitConfig.PREDICTION_LIMITS[user.subscription_tier]
        else:
            limit = 100  # Default fallback
    else:
        # Use IP-based limiting for unauthenticated requests
        identifier = f"ip_{client_ip}"
        if limit_type == "auth":
            endpoint = request.url.path.split("/")[-1]
            limit = RateLimitConfig.AUTH_LIMITS.get(endpoint, 20)
        else:
            limit = 20  # Strict limit for unauthenticated requests
    
    # Check rate limit
    if not rate_limiter.is_allowed(identifier, limit):
        remaining = rate_limiter.get_remaining_requests(identifier, limit)
        reset_time = rate_limiter.get_reset_time(identifier)
        
        headers = {
            "X-RateLimit-Limit": str(limit),
            "X-RateLimit-Remaining": str(remaining),
        }
        
        if reset_time:
            headers["X-RateLimit-Reset"] = str(int(reset_time.timestamp()))
        
        logger.warning(f"Rate limit exceeded for {identifier}. Limit: {limit}/hour")
        
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail={
                "error": "Rate limit exceeded",
                "limit": limit,
                "remaining": remaining,
                "reset_time": reset_time.isoformat() if reset_time else None,
                "message": f"Too many requests. Limit: {limit} requests per hour."
            },
            headers=headers
        )


def add_rate_limit_headers(request: Request, user: Optional[User] = None) -> Dict[str, str]:
    """Add rate limit headers to response"""
    client_ip = request.client.host
    
    if user:
        identifier = f"user_{user.id}"
        limit = RateLimitConfig.GENERAL_LIMITS[user.subscription_tier]
    else:
        identifier = f"ip_{client_ip}"
        limit = 20
    
    remaining = rate_limiter.get_remaining_requests(identifier, limit)
    reset_time = rate_limiter.get_reset_time(identifier)
    
    headers = {
        "X-RateLimit-Limit": str(limit),
        "X-RateLimit-Remaining": str(remaining),
    }
    
    if reset_time:
        headers["X-RateLimit-Reset"] = str(int(reset_time.timestamp()))
    
    return headers


# Dependency functions for FastAPI
def rate_limit_general(request: Request, user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """Rate limit dependency for general API endpoints"""
    check_rate_limit(request, "general", user, db)
    return True


def rate_limit_predictions(request: Request, user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """Rate limit dependency for prediction endpoints"""
    check_rate_limit(request, "prediction", user, db)
    return True


def rate_limit_auth(request: Request):
    """Rate limit dependency for authentication endpoints"""
    check_rate_limit(request, "auth")
    return True


def subscription_check(operation: str, **kwargs):
    """
    Dependency factory for subscription validation
    
    Args:
        operation: Type of operation to validate
        **kwargs: Additional parameters for validation
    """
    def _check_subscription(
        user: User = Depends(get_current_user),
        db: Session = Depends(get_db)
    ):
        subscription_service = SubscriptionService(db)
        subscription_service.validate_subscription_access(user, operation, **kwargs)
        
        # Increment API usage for non-auth operations
        if operation in ["api_call", "prediction"]:
            subscription_service.increment_api_usage(user)
        
        return user
    
    return _check_subscription
