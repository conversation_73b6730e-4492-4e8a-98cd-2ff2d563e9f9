"""
Tests for prediction functionality (stocks and crypto)
"""

import pytest
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
import pandas as pd
import numpy as np

from app.services.stock_service import StockService
from app.services.crypto_service import CryptoService


class TestStockService:
    """Test stock prediction service."""
    
    @patch('app.services.stock_service.yf.Ticker')
    def test_get_stock_data(self, mock_ticker, db_session):
        """Test fetching stock data."""
        # Mock yfinance data
        mock_data = pd.DataFrame({
            'Close': [150.0, 151.0, 149.5, 152.0, 153.5],
            'Volume': [1000000, 1100000, 950000, 1200000, 1150000],
            'High': [151.5, 152.0, 150.0, 153.0, 154.0],
            'Low': [149.0, 150.0, 148.5, 151.0, 152.0],
            'Open': [150.5, 150.8, 150.2, 151.5, 152.8]
        })
        # Mock the ticker and its history method
        mock_ticker_instance = Mock()
        mock_ticker_instance.history.return_value = mock_data
        mock_ticker.return_value = mock_ticker_instance

        stock_service = StockService()
        data = stock_service.get_stock_data("AAPL", period="1mo")

        assert data is not None
        assert len(data) == 5
        assert 'Close' in data.columns
        mock_ticker.assert_called_once_with("AAPL")
        mock_ticker_instance.history.assert_called_once_with(period="1mo")
    
    def test_preprocess_data(self, db_session):
        """Test data preprocessing."""
        stock_service = StockService()
        
        # Create sample data
        data = pd.DataFrame({
            'Close': [100, 101, 102, 103, 104, 105, 106, 107, 108, 109],
            'Volume': [1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900]
        })
        
        X, y, scaler = stock_service.preprocess_data(data, sequence_length=3)
        
        assert X is not None
        assert y is not None
        assert scaler is not None
        assert X.shape[1] == 3  # sequence_length
        assert X.shape[2] == 2  # features (Close, Volume)
    
    def test_create_lstm_model(self, db_session):
        """Test LSTM model creation."""
        stock_service = StockService()
        
        model = stock_service.create_lstm_model(input_shape=(10, 2))
        
        assert model is not None
        assert len(model.layers) > 0
        
        # Check model structure
        assert model.input_shape == (None, 10, 2)
        assert model.output_shape == (None, 1)
    
    @pytest.mark.asyncio
    @patch('app.services.stock_service.yf.download')
    async def test_predict_stock_price(self, mock_download, db_session, sample_stock_data):
        """Test stock price prediction."""
        # Mock yfinance data
        mock_data = pd.DataFrame({
            'Close': np.random.rand(100) * 100 + 100,  # Random prices around 100-200
            'Volume': np.random.rand(100) * 1000000 + 500000,
            'High': np.random.rand(100) * 100 + 105,
            'Low': np.random.rand(100) * 100 + 95,
            'Open': np.random.rand(100) * 100 + 100
        })
        mock_download.return_value = mock_data
        
        stock_service = StockService()
        
        # Mock the model training and prediction
        with patch.object(stock_service, 'train_model') as mock_train:
            mock_model = Mock()
            mock_model.predict.return_value = np.array([[150.5], [151.2], [152.0]])
            mock_train.return_value = (mock_model, Mock())
            
            result = await stock_service.predict_stock_price("AAPL", days_ahead=3)
            
            assert result is not None
            assert "symbol" in result
            assert "predicted_prices" in result
            assert "confidence_score" in result
            assert result["symbol"] == "AAPL"
            assert len(result["predicted_prices"]) == 3


class TestCryptoService:
    """Test crypto prediction service."""
    
    @patch('app.services.crypto_service.requests.get')
    def test_get_crypto_data(self, mock_get, db_session):
        """Test fetching crypto data."""
        # Mock CoinGecko API response
        mock_response = Mock()
        mock_response.json.return_value = {
            "prices": [[1640995200000, 47000], [1641081600000, 47500], [1641168000000, 46800]],
            "market_caps": [[1640995200000, 890000000000], [1641081600000, 900000000000], [1641168000000, 885000000000]],
            "total_volumes": [[1640995200000, 25000000000], [1641081600000, 26000000000], [1641168000000, 24000000000]]
        }
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        crypto_service = CryptoService()
        data = crypto_service.get_crypto_data("bitcoin", days=30)
        
        assert data is not None
        assert len(data) == 3
        assert 'price' in data.columns
        mock_get.assert_called_once()
    
    @patch('app.services.crypto_service.requests.get')
    @pytest.mark.asyncio
    async def test_predict_crypto_price(self, mock_get, db_session):
        """Test crypto price prediction."""
        # Mock CoinGecko API response with more data points
        prices = [[1640995200000 + i * 86400000, 47000 + i * 100] for i in range(100)]
        market_caps = [[1640995200000 + i * 86400000, 890000000000 + i * 1000000000] for i in range(100)]
        volumes = [[1640995200000 + i * 86400000, 25000000000 + i * 100000000] for i in range(100)]

        mock_response = Mock()
        mock_response.json.return_value = {
            "prices": prices,
            "market_caps": market_caps,
            "total_volumes": volumes
        }
        mock_response.status_code = 200
        mock_get.return_value = mock_response

        crypto_service = CryptoService()

        # Mock the model training and prediction
        with patch.object(crypto_service, 'train_model') as mock_train:
            mock_model = Mock()
            mock_model.predict.return_value = np.array([[47500.0], [48000.0], [48200.0]])
            mock_train.return_value = (mock_model, Mock())

            result = await crypto_service.predict_crypto_price("BTC", days_ahead=3)

            assert result is not None
            assert "symbol" in result
            assert "predicted_prices" in result
            assert "confidence_score" in result
            assert result["symbol"] == "BTC"
            assert len(result["predicted_prices"]) == 3


class TestPredictionEndpoints:
    """Test prediction API endpoints."""
    
    def test_stock_prediction_endpoint_success(self, client: TestClient, auth_headers: dict):
        """Test successful stock prediction request."""
        prediction_data = {
            "symbol": "AAPL",
            "days_ahead": 5,
            "include_volume_analysis": True,
            "include_sentiment_analysis": False
        }
        
        with patch('app.services.stock_service.StockService.predict_stock_price') as mock_predict:
            from datetime import datetime
            mock_predict.return_value = {
                "symbol": "AAPL",
                "current_price": 150.25,
                "predicted_prices": [151.0, 152.5, 154.0, 155.2, 156.8],
                "prediction_dates": ["2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-06"],
                "confidence_score": 0.81,
                "model_version": "v1.0.0",
                "created_at": datetime(2024, 1, 1, 12, 0, 0)
            }
            
            response = client.post(
                "/api/v1/stocks/AAPL/predict",
                json=prediction_data,
                headers=auth_headers
            )

            if response.status_code != 200:
                print(f"Response status: {response.status_code}")
                print(f"Response content: {response.text}")

            assert response.status_code == 200
            data = response.json()
            assert data["symbol"] == "AAPL"
            assert "predicted_prices" in data
            assert len(data["predicted_prices"]) == 5
            assert "prediction_dates" in data
            assert len(data["prediction_dates"]) == 5
            assert data["confidence_score"] == 0.81
    
    def test_crypto_prediction_endpoint_success(self, client: TestClient, auth_headers: dict):
        """Test successful crypto prediction request."""
        prediction_data = {
            "symbol": "bitcoin",
            "days_ahead": 5,
            "include_volume_analysis": True
        }
        
        with patch('app.services.crypto_service.CryptoService.predict_crypto_price') as mock_predict:
            from datetime import datetime
            mock_predict.return_value = {
                "symbol": "bitcoin",
                "current_price": 45000.0,
                "predicted_prices": [45500.0, 46000.0, 46800.0, 47200.0, 47900.0],
                "prediction_dates": ["2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-06"],
                "confidence_score": 0.78,
                "volatility_score": 0.85,
                "model_version": "v1.0.0",
                "created_at": datetime(2024, 1, 1, 12, 0, 0)
            }
            
            response = client.post(
                "/api/v1/crypto/bitcoin/predict",
                json=prediction_data,
                headers=auth_headers
            )

            if response.status_code != 200:
                print(f"Crypto response status: {response.status_code}")
                print(f"Crypto response content: {response.text}")

            assert response.status_code == 200
            data = response.json()
            assert data["symbol"] == "bitcoin"
            assert "predicted_prices" in data
            assert len(data["predicted_prices"]) == 5
            assert "prediction_dates" in data
            assert len(data["prediction_dates"]) == 5
            assert data["confidence_score"] == 0.78
    
    def test_prediction_requires_authentication(self, client: TestClient):
        """Test that prediction endpoints require authentication."""
        prediction_data = {
            "symbol": "AAPL",
            "days_ahead": 5
        }
        
        response = client.post("/api/v1/stocks/AAPL/predict", json=prediction_data)
        assert response.status_code == 401
    
    def test_prediction_respects_subscription_limits(self, client: TestClient, auth_headers: dict):
        """Test that predictions respect subscription limits."""
        prediction_data = {
            "symbol": "AAPL",
            "days_ahead": 100  # Exceeds FREE tier limit of 30 days
        }
        
        response = client.post(
            "/api/v1/stocks/AAPL/predict",
            json=prediction_data,
            headers=auth_headers
        )
        
        # Should fail due to subscription limits
        assert response.status_code in [400, 403]
        if response.status_code == 400:
            assert "days ahead limit" in response.json()["detail"]
    
    def test_invalid_symbol_handling(self, client: TestClient, auth_headers: dict):
        """Test handling of invalid stock/crypto symbols."""
        prediction_data = {
            "symbol": "INVALID_SYMBOL",
            "days_ahead": 5
        }
        
        response = client.post(
            "/api/v1/stocks/INVALID_SYMBOL/predict",
            json=prediction_data,
            headers=auth_headers
        )
        
        # Should handle invalid symbols gracefully
        assert response.status_code in [400, 404]
    
    def test_prediction_validation(self, client: TestClient, auth_headers: dict):
        """Test prediction request validation."""
        # Test missing required fields
        response = client.post(
            "/api/v1/stocks/AAPL/predict",
            json={},
            headers=auth_headers
        )
        assert response.status_code == 422  # Validation error
        
        # Test invalid days_ahead
        invalid_data = {
            "symbol": "AAPL",
            "days_ahead": -1  # Invalid negative value
        }
        
        response = client.post(
            "/api/v1/stocks/AAPL/predict",
            json=invalid_data,
            headers=auth_headers
        )
        assert response.status_code == 422
    
    def test_prediction_caching(self, client: TestClient, auth_headers: dict):
        """Test prediction result caching."""
        prediction_data = {
            "symbol": "AAPL",
            "days_ahead": 5
        }
        
        with patch('app.services.stock_service.StockService.predict_stock_price') as mock_predict:
            mock_result = {
                "symbol": "AAPL",
                "current_price": 150.0,
                "predicted_prices": [151.0, 152.0, 153.0, 154.0, 155.0],
                "prediction_dates": ["2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-06"],
                "confidence_score": 0.85,
                "model_version": "1.0",
                "created_at": "2024-01-01T12:00:00"
            }
            # Mock async method
            async def async_mock(*args, **kwargs):
                return mock_result
            mock_predict.side_effect = async_mock
            
            # First request
            response1 = client.post(
                "/api/v1/stocks/AAPL/predict",
                json=prediction_data,
                headers=auth_headers
            )
            
            # Second request (should potentially use cache)
            response2 = client.post(
                "/api/v1/stocks/AAPL/predict",
                json=prediction_data,
                headers=auth_headers
            )
            
            assert response1.status_code == 200
            assert response2.status_code == 200
            
            # Both should return the same data
            assert response1.json() == response2.json()


class TestPredictionAccuracy:
    """Test prediction accuracy and model performance."""
    
    def test_model_accuracy_calculation(self, db_session):
        """Test model accuracy calculation."""
        stock_service = StockService()
        
        # Mock actual vs predicted values
        actual = np.array([100, 101, 102, 103, 104])
        predicted = np.array([99.5, 101.2, 101.8, 103.1, 104.3])
        
        accuracy = stock_service.calculate_accuracy(actual, predicted)
        
        assert 0 <= accuracy <= 1
        assert accuracy > 0.9  # Should be high accuracy for close predictions
    
    def test_confidence_score_calculation(self, db_session):
        """Test confidence score calculation."""
        stock_service = StockService()
        
        # Mock prediction variance
        predictions = np.array([[100], [101], [102], [103], [104]])
        
        confidence = stock_service.calculate_confidence(predictions)
        
        assert 0 <= confidence <= 1
    
    def test_prediction_consistency(self, db_session):
        """Test that predictions are consistent for same inputs."""
        stock_service = StockService()
        
        # Create deterministic test data
        data = pd.DataFrame({
            'Close': [100 + i for i in range(50)],
            'Volume': [1000000 + i * 10000 for i in range(50)]
        })
        
        with patch.object(stock_service, 'get_stock_data', return_value=data):
            with patch('tensorflow.keras.models.Sequential') as mock_model_class:
                mock_model = Mock()
                mock_model.predict.return_value = np.array([[150.0], [151.0], [152.0]])
                mock_model_class.return_value = mock_model
                
                # Make two predictions with same parameters
                result1 = stock_service.predict_stock_price("AAPL", days_ahead=3)
                result2 = stock_service.predict_stock_price("AAPL", days_ahead=3)
                
                # Results should be similar (allowing for some randomness in model)
                assert result1["symbol"] == result2["symbol"]
                assert len(result1["predictions"]) == len(result2["predictions"])


class TestPredictionErrorHandling:
    """Test error handling in prediction services."""
    
    def test_network_error_handling(self, db_session):
        """Test handling of network errors when fetching data."""
        stock_service = StockService()

        with patch('app.services.stock_service.yf.Ticker') as mock_ticker:
            mock_ticker_instance = Mock()
            mock_ticker_instance.history.side_effect = Exception("Network error")
            mock_ticker.return_value = mock_ticker_instance

            with pytest.raises(Exception):
                stock_service.get_stock_data("AAPL")
    
    @pytest.mark.asyncio
    async def test_insufficient_data_handling(self, db_session):
        """Test handling when insufficient data is available."""
        stock_service = StockService()

        # Mock insufficient data
        insufficient_data = pd.DataFrame({
            'Close': [100, 101],  # Only 2 data points
            'Volume': [1000000, 1100000]
        })

        with patch.object(stock_service, 'get_stock_data', return_value=insufficient_data):
            with pytest.raises(Exception) as exc_info:
                await stock_service.predict_stock_price("AAPL", days_ahead=5)

            assert "insufficient data" in str(exc_info.value).lower()
    
    @pytest.mark.asyncio
    async def test_model_training_failure(self, db_session):
        """Test handling of model training failures."""
        stock_service = StockService()

        # Mock sufficient data
        data = pd.DataFrame({
            'Close': [100 + i for i in range(100)],
            'Volume': [1000000 + i * 10000 for i in range(100)]
        })

        with patch.object(stock_service, 'get_stock_data', return_value=data):
            with patch.object(stock_service, 'train_model') as mock_train:
                mock_train.side_effect = Exception("Model training failed")

                # Should return fallback prediction, not raise exception
                result = await stock_service.predict_stock_price("AAPL", days_ahead=5)

                assert result is not None
                assert "symbol" in result
                assert "predicted_prices" in result
                assert result["symbol"] == "AAPL"
                assert len(result["predicted_prices"]) == 5


class TestPredictionIntegration:
    """Test integration between prediction services and database."""

    def test_prediction_storage(self, client: TestClient, auth_headers: dict, db_session):
        """Test that predictions are stored in database."""
        prediction_data = {
            "symbol": "AAPL",
            "days_ahead": 3
        }

        # Mock the data fetching method instead of the entire prediction method
        # so that database storage logic still runs
        with patch('app.services.stock_service.StockService.get_stock_data') as mock_get_data:
            # Create mock data that looks like yfinance data
            import pandas as pd
            import numpy as np
            from datetime import datetime, timedelta

            # Create mock historical data with enough data points for LSTM
            dates = pd.date_range(start='2023-01-01', end='2024-01-01', freq='D')
            mock_data = pd.DataFrame({
                'Open': np.random.uniform(140, 160, len(dates)),
                'High': np.random.uniform(145, 165, len(dates)),
                'Low': np.random.uniform(135, 155, len(dates)),
                'Close': np.random.uniform(140, 160, len(dates)),
                'Volume': np.random.randint(1000000, 10000000, len(dates))
            }, index=dates)

            mock_get_data.return_value = mock_data

            response = client.post(
                "/api/v1/stocks/AAPL/predict",
                json=prediction_data,
                headers=auth_headers
            )

            assert response.status_code == 200

            # Check if prediction was stored in database
            from app.models.prediction import Prediction
            predictions = db_session.query(Prediction).filter(
                Prediction.symbol == "AAPL"
            ).all()

            assert len(predictions) > 0

    def test_prediction_history_retrieval(self, client: TestClient, auth_headers: dict):
        """Test retrieving prediction history."""
        # This would test an endpoint for getting prediction history
        # Implementation depends on whether such endpoint exists

        response = client.get("/api/v1/predictions/history", headers=auth_headers)

        # This might return 404 if endpoint doesn't exist yet
        assert response.status_code in [200, 404]

        if response.status_code == 200:
            data = response.json()
            # The endpoint returns a list of PredictionRecord objects directly
            assert isinstance(data, list)
