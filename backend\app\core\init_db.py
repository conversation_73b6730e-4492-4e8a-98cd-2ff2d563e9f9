"""
Database initialization script
"""

import os
from sqlalchemy import create_engine
from app.core.config import settings
from app.core.database import Base
from app.models import User, Prediction, PredictionAccuracy, MLModel, TrainingHistory, PredictionCache  # Import all models


def create_database():
    """Create database and all tables"""
    
    # Create data directory if it doesn't exist
    if settings.DATABASE_TYPE == "sqlite":
        data_dir = os.path.dirname(settings.SQLITE_DB_PATH)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
            print(f"Created data directory: {data_dir}")
    
    # Create engine
    engine = create_engine(
        settings.SQLALCHEMY_DATABASE_URI,
        echo=True  # Show SQL statements
    )
    
    # Create all tables
    print("Creating database tables...")
    Base.metadata.create_all(bind=engine)
    print("Database tables created successfully!")
    
    return engine


def drop_database():
    """Drop all database tables"""
    engine = create_engine(settings.SQLALCHEMY_DATABASE_URI)
    print("Dropping all database tables...")
    Base.metadata.drop_all(bind=engine)
    print("Database tables dropped successfully!")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "drop":
        drop_database()
    else:
        create_database()
