#!/usr/bin/env python3
"""Simple test to verify real implementation"""

import asyncio
import yfinance as yf

async def test_real_implementation():
    print('=== TESTING REAL IMPLEMENTATION ===')
    print()
    
    # Test 1: PyTorch availability
    print('1. PyTorch Import Test:')
    try:
        import torch
        print(f'✅ PyTorch version: {torch.__version__}')
        print(f'✅ CUDA available: {torch.cuda.is_available()}')
    except ImportError as e:
        print(f'❌ PyTorch import failed: {e}')
    print()
    
    # Test 2: Real market data
    print('2. Real Market Data Test:')
    try:
        ticker = yf.Ticker('AAPL')
        data = ticker.history(period='5d')
        if not data.empty:
            current_price = data['Close'].iloc[-1]
            print(f'✅ AAPL current price: ${current_price:.2f}')
            print(f'✅ Data points: {len(data)} days')
        else:
            print('❌ No market data retrieved')
    except Exception as e:
        print(f'❌ Market data error: {e}')
    print()
    
    # Test 3: Service import
    print('3. Service Import Test:')
    try:
        from app.services.stock_service import StockService, PYTORCH_AVAILABLE
        print(f'✅ StockService imported')
        print(f'✅ PyTorch available in service: {PYTORCH_AVAILABLE}')
        
        service = StockService()
        print(f'✅ StockService instance created')
    except Exception as e:
        print(f'❌ Service import error: {e}')
    print()
    
    # Test 4: Simple prediction test
    print('4. Simple Prediction Test:')
    try:
        from app.services.stock_service import StockService
        service = StockService()
        
        # Test fallback prediction (should use real trend analysis)
        result = service._generate_fallback_prediction('AAPL', 2)
        print(f'✅ Fallback prediction generated')
        print(f'   Symbol: {result["symbol"]}')
        print(f'   Current Price: ${result["current_price"]:.2f}')
        print(f'   Predictions: {[f"${p:.2f}" for p in result["predicted_prices"]]}')
        print(f'   Model Version: {result["model_version"]}')
        print(f'   Note: {result.get("note", "N/A")}')
        
        # Check if it's using real data
        if result["current_price"] != 150.0:  # Not the old mock price
            print('✅ Using REAL market data (not mock $150)')
        else:
            print('❌ Still using mock data')
            
    except Exception as e:
        print(f'❌ Prediction test error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_real_implementation())
