import React, { useState } from "react";
import {
  Container,
  Grid,
  Paper,
  Typography,
  TextField,
  Button,
  Box,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Alert,
  FormControlLabel,
  Switch,
  Autocomplete,
} from "@mui/material";
import {
  CurrencyBitcoin,
  Search,
  Timeline,
  Assessment,
  TrendingUp,
} from "@mui/icons-material";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

const CryptoPrediction: React.FC = () => {
  const [selectedCrypto, setSelectedCrypto] = useState<string>("");
  const [daysAhead, setDaysAhead] = useState<number>(30);
  const [includeVolumeAnalysis, setIncludeVolumeAnalysis] =
    useState<boolean>(true);
  const [includeSentimentAnalysis, setIncludeSentimentAnalysis] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [prediction, setPrediction] = useState<any>(null);
  const [error, setError] = useState<string>("");

  // Mock crypto options
  const cryptoOptions = [
    { label: "Bitcoin (BTC)", value: "BTC" },
    { label: "Ethereum (ETH)", value: "ETH" },
    { label: "Cardano (ADA)", value: "ADA" },
    { label: "Solana (SOL)", value: "SOL" },
    { label: "Polkadot (DOT)", value: "DOT" },
    { label: "Chainlink (LINK)", value: "LINK" },
    { label: "Polygon (MATIC)", value: "MATIC" },
  ];

  // Mock prediction data with higher volatility for crypto
  const mockPredictionData = [
    { date: "2024-01-01", actual: 45000, predicted: 45200 },
    { date: "2024-01-02", actual: 46500, predicted: 46800 },
    { date: "2024-01-03", actual: 44200, predicted: 44000 },
    { date: "2024-01-04", actual: 47800, predicted: 47500 },
    { date: "2024-01-05", actual: 48200, predicted: 48500 },
    { date: "2024-01-06", actual: null, predicted: 49000 },
    { date: "2024-01-07", actual: null, predicted: 50200 },
    { date: "2024-01-08", actual: null, predicted: 48800 },
  ];

  const handlePredict = async () => {
    if (!selectedCrypto) {
      setError("Please select a cryptocurrency");
      return;
    }

    setLoading(true);
    setError("");

    try {
      // Real API call to backend
      const token = localStorage.getItem("token");
      const response = await fetch(
        `http://localhost:8000/api/v1/crypto/${selectedCrypto}/predict`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            days_ahead: daysAhead,
          }),
        }
      );

      if (!response.ok) {
        if (response.status === 401) {
          setError("Please log in to make predictions");
          return;
        }
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();

      // Convert API response to frontend format
      const predictionData = data.predicted_prices.map(
        (price: number, index: number) => ({
          date: new Date(Date.now() + (index + 1) * 24 * 60 * 60 * 1000)
            .toISOString()
            .split("T")[0],
          actual: null,
          predicted: price,
        })
      );

      setPrediction({
        symbol: data.symbol,
        currentPrice: data.current_price,
        predictedPrices: data.predicted_prices,
        confidenceScore: data.confidence_score,
        volatilityScore: data.volatility_score || 0.85, // Default if not provided
        modelVersion: data.model_version,
        data: predictionData,
        note: data.note,
        modelType: data.model_type,
      });
    } catch (err) {
      console.error("Prediction error:", err);
      setError("Failed to generate prediction. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Cryptocurrency Price Prediction
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          AI-powered LSTM predictions for cryptocurrency market analysis
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Prediction Form */}
        <Grid item xs={12} md={4}>
          <Paper
            sx={{
              p: 3,
              background: "linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)",
            }}
          >
            <Typography variant="h6" gutterBottom>
              Generate Prediction
            </Typography>

            <Box sx={{ mt: 3 }}>
              <Autocomplete
                options={cryptoOptions}
                getOptionLabel={(option) => option.label}
                onChange={(_, value) => setSelectedCrypto(value?.value || "")}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Select Cryptocurrency"
                    variant="outlined"
                    fullWidth
                    InputProps={{
                      ...params.InputProps,
                      startAdornment: (
                        <Search sx={{ mr: 1, color: "text.secondary" }} />
                      ),
                    }}
                  />
                )}
                sx={{ mb: 3 }}
              />

              <TextField
                label="Days Ahead"
                type="number"
                value={daysAhead}
                onChange={(e) => setDaysAhead(Number(e.target.value))}
                fullWidth
                inputProps={{ min: 1, max: 90 }}
                sx={{ mb: 3 }}
              />

              <FormControlLabel
                control={
                  <Switch
                    checked={includeVolumeAnalysis}
                    onChange={(e) => setIncludeVolumeAnalysis(e.target.checked)}
                  />
                }
                label="Include Volume Analysis"
                sx={{ mb: 2 }}
              />

              <FormControlLabel
                control={
                  <Switch
                    checked={includeSentimentAnalysis}
                    onChange={(e) =>
                      setIncludeSentimentAnalysis(e.target.checked)
                    }
                  />
                }
                label="Include Sentiment Analysis"
                sx={{ mb: 3 }}
              />

              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              <Button
                variant="contained"
                fullWidth
                size="large"
                onClick={handlePredict}
                disabled={loading}
                startIcon={
                  loading ? <CircularProgress size={20} /> : <Timeline />
                }
              >
                {loading ? "Generating Prediction..." : "Predict Price"}
              </Button>
            </Box>
          </Paper>

          {/* Current Crypto Info */}
          {selectedCrypto && (
            <Paper
              sx={{
                p: 3,
                mt: 3,
                background: "linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)",
              }}
            >
              <Typography variant="h6" gutterBottom>
                Current {selectedCrypto} Info
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="h4" color="primary">
                  $45,200
                </Typography>
                <Box
                  sx={{ display: "flex", alignItems: "center", gap: 1, mt: 1 }}
                >
                  <TrendingUp sx={{ color: "success.main" }} />
                  <Typography color="success.main">+3.2% (+$1,400)</Typography>
                </Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ mt: 1 }}
                >
                  24h Volume: $25.4B | Market Cap: $850B
                </Typography>
                <Chip
                  label="High Volatility"
                  color="warning"
                  size="small"
                  sx={{ mt: 1 }}
                />
              </Box>
            </Paper>
          )}
        </Grid>

        {/* Prediction Results */}
        <Grid item xs={12} md={8}>
          {prediction ? (
            <Paper
              sx={{
                p: 3,
                background: "linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)",
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  mb: 3,
                }}
              >
                <Typography variant="h6">
                  Prediction Results for {prediction.symbol}
                </Typography>
                <Box sx={{ display: "flex", gap: 1 }}>
                  <Chip
                    label={`${(prediction.confidenceScore * 100).toFixed(
                      1
                    )}% Confidence`}
                    color="success"
                    icon={<Assessment />}
                  />
                  <Chip
                    label={`${(prediction.volatilityScore * 100).toFixed(
                      1
                    )}% Volatility`}
                    color="warning"
                  />
                </Box>
              </Box>

              {/* Chart */}
              <Box sx={{ height: 400, mb: 3 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={prediction.data}>
                    <CartesianGrid
                      strokeDasharray="3 3"
                      stroke="rgba(255,255,255,0.1)"
                    />
                    <XAxis dataKey="date" stroke="#ffffff" />
                    <YAxis stroke="#ffffff" />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: "#1a1d3a",
                        border: "1px solid #2a2d5a",
                        borderRadius: "8px",
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="actual"
                      stroke="#4caf50"
                      strokeWidth={2}
                      name="Actual Price"
                      connectNulls={false}
                    />
                    <Line
                      type="monotone"
                      dataKey="predicted"
                      stroke="#ff9800"
                      strokeWidth={2}
                      strokeDasharray="5 5"
                      name="Predicted Price"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>

              {/* Prediction Summary */}
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Card sx={{ background: "rgba(255, 152, 0, 0.1)" }}>
                    <CardContent>
                      <Typography color="warning.main" gutterBottom>
                        Current Price
                      </Typography>
                      <Typography variant="h6">
                        ${prediction.currentPrice.toLocaleString()}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card sx={{ background: "rgba(76, 175, 80, 0.1)" }}>
                    <CardContent>
                      <Typography color="success.main" gutterBottom>
                        30-Day Target
                      </Typography>
                      <Typography variant="h6">
                        $
                        {prediction.predictedPrices[
                          prediction.predictedPrices.length - 1
                        ].toLocaleString()}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card sx={{ background: "rgba(244, 67, 54, 0.1)" }}>
                    <CardContent>
                      <Typography color="error.main" gutterBottom>
                        Volatility Risk
                      </Typography>
                      <Typography variant="h6">High</Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Card sx={{ background: "rgba(156, 39, 176, 0.1)" }}>
                    <CardContent>
                      <Typography color="secondary.main" gutterBottom>
                        Model Version
                      </Typography>
                      <Typography variant="h6">
                        {prediction.modelVersion}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              {/* Crypto-specific insights */}
              <Box sx={{ mt: 3 }}>
                <Alert severity="info">
                  <Typography variant="subtitle2" gutterBottom>
                    Cryptocurrency Market Insights
                  </Typography>
                  <Typography variant="body2">
                    • Crypto markets are highly volatile and predictions have
                    lower confidence scores
                    <br />
                    • Consider market sentiment and regulatory news when making
                    decisions
                    <br />• Use proper risk management and never invest more
                    than you can afford to lose
                  </Typography>
                </Alert>
              </Box>
            </Paper>
          ) : (
            <Paper
              sx={{
                p: 6,
                textAlign: "center",
                background: "linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)",
              }}
            >
              <CurrencyBitcoin
                sx={{ fontSize: 64, color: "text.secondary", mb: 2 }}
              />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No Prediction Generated
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Select a cryptocurrency and click "Predict Price" to generate
                AI-powered predictions
              </Typography>
            </Paper>
          )}
        </Grid>
      </Grid>
    </Container>
  );
};

export default CryptoPrediction;
