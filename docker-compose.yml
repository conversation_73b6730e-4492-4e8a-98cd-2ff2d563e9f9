version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: tradingbot_postgres
    environment:
      POSTGRES_USER: tradingbot
      POSTGRES_PASSWORD: password
      POSTGRES_DB: tradingbot_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - tradingbot_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: tradingbot_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - tradingbot_network

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: tradingbot_backend
    ports:
      - "8000:8000"
    environment:
      - POSTGRES_SERVER=postgres
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - ./data:/app/data
      - ./models:/app/models
    networks:
      - tradingbot_network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: tradingbot_frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - tradingbot_network

  # Celery Worker (for background tasks)
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: tradingbot_celery
    environment:
      - POSTGRES_SERVER=postgres
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - ./data:/app/data
      - ./models:/app/models
    networks:
      - tradingbot_network
    command: celery -A app.core.celery worker --loglevel=info

  # Flower (Celery monitoring)
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: tradingbot_flower
    ports:
      - "5555:5555"
    environment:
      - REDIS_HOST=redis
    depends_on:
      - redis
    networks:
      - tradingbot_network
    command: celery -A app.core.celery flower --port=5555

volumes:
  postgres_data:
  redis_data:

networks:
  tradingbot_network:
    driver: bridge
