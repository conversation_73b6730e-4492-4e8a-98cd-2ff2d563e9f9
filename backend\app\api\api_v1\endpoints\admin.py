"""
Admin endpoints for managing users and subscriptions
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query, Request
from pydantic import BaseModel
from sqlalchemy.orm import Session
from datetime import datetime

from app.core.database import get_db
from app.core.auth import get_current_active_user
from app.core.rate_limiter import rate_limit_general
from app.models.user import User, SubscriptionTier
from app.services.subscription_service import SubscriptionService
from app.services.database_service import DatabaseService

router = APIRouter()


class UserSummary(BaseModel):
    """Summary information about a user"""
    id: int
    email: str
    full_name: Optional[str]
    subscription_tier: str
    is_active: bool
    is_verified: bool
    api_calls_used: int
    api_calls_limit: int
    created_at: datetime
    total_predictions: int


class AdminStatsResponse(BaseModel):
    """Admin dashboard statistics"""
    total_users: int
    active_users: int
    total_predictions: int
    subscription_breakdown: dict
    api_usage_stats: dict


class UserUpdateRequest(BaseModel):
    """Request model for updating user"""
    subscription_tier: Optional[str] = None
    is_active: Optional[bool] = None


class SystemHealthResponse(BaseModel):
    """System health status response"""
    status: str
    database: dict
    external_apis: dict
    memory_usage: dict
    uptime: str


class PredictionsResponse(BaseModel):
    """Response model for predictions list"""
    predictions: List[dict]
    total: int
    limit: int
    offset: int


def check_admin_access(current_user: User = Depends(get_current_active_user)) -> User:
    """Check if current user has admin access"""
    # For demo purposes, we'll check if user email contains 'admin'
    # In production, you'd have a proper admin role system
    if "admin" not in current_user.email.lower():
        raise HTTPException(
            status_code=403,
            detail="Admin access required"
        )
    return current_user


@router.get("/stats", response_model=AdminStatsResponse)
async def get_admin_stats(
    request: Request,
    admin_user: User = Depends(check_admin_access),
    db: Session = Depends(get_db),
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Get admin dashboard statistics"""
    try:
        db_service = DatabaseService(db)
        
        # Get all users
        all_users = db.query(User).all()
        total_users = len(all_users)
        active_users = len([u for u in all_users if u.is_active])
        
        # Count users by tier
        subscription_breakdown = {}
        for tier in SubscriptionTier:
            subscription_breakdown[tier.value] = len([u for u in all_users if u.subscription_tier == tier])

        # Get total predictions
        total_predictions = len(db_service.get_all_predictions())

        # Calculate API usage stats
        total_api_calls = sum(u.api_calls_used for u in all_users)
        predictions_today = total_predictions  # Simplified for now
        average_daily_usage = total_api_calls // max(1, total_users)  # Avoid division by zero

        api_usage_stats = {
            "total_api_calls": total_api_calls,
            "predictions_today": predictions_today,
            "average_daily_usage": average_daily_usage
        }

        return AdminStatsResponse(
            total_users=total_users,
            active_users=active_users,
            total_predictions=total_predictions,
            subscription_breakdown=subscription_breakdown,
            api_usage_stats=api_usage_stats
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving admin statistics: {str(e)}"
        )


@router.get("/users")
async def get_all_users(
    request: Request,
    admin_user: User = Depends(check_admin_access),
    db: Session = Depends(get_db),
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    tier_filter: Optional[SubscriptionTier] = Query(None),
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Get list of all users with pagination and filtering"""
    try:
        db_service = DatabaseService(db)
        
        # Build query
        query = db.query(User)
        
        # Apply tier filter if specified
        if tier_filter:
            query = query.filter(User.subscription_tier == tier_filter)
        
        # Apply pagination
        users = query.offset(offset).limit(limit).all()
        
        # Build response
        result = []
        for user in users:
            user_predictions = db_service.get_user_predictions(user.id, 10000)  # Get all for count
            
            result.append(UserSummary(
                id=user.id,
                email=user.email,
                full_name=user.full_name,
                subscription_tier=user.subscription_tier.value,
                is_active=user.is_active,
                is_verified=user.is_verified,
                api_calls_used=user.api_calls_used,
                api_calls_limit=user.api_calls_limit,
                created_at=user.created_at,
                total_predictions=len(user_predictions)
            ))
        
        return {"users": result}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving users: {str(e)}"
        )


@router.post("/users/{user_id}/upgrade")
async def admin_upgrade_user(
    user_id: int,
    new_tier: SubscriptionTier,
    request: Request,
    admin_user: User = Depends(check_admin_access),
    db: Session = Depends(get_db),
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Admin endpoint to upgrade a user's subscription"""
    try:
        # Get the target user
        target_user = db.query(User).filter(User.id == user_id).first()
        if not target_user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Upgrade the subscription
        subscription_service = SubscriptionService(db)
        updated_user = subscription_service.upgrade_subscription(target_user, new_tier)
        
        return {
            "message": f"Successfully upgraded user {target_user.email} to {new_tier.value}",
            "user_id": user_id,
            "old_tier": target_user.subscription_tier.value,
            "new_tier": updated_user.subscription_tier.value,
            "upgraded_by": admin_user.email
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error upgrading user subscription: {str(e)}"
        )


@router.post("/users/{user_id}/reset-usage")
async def admin_reset_user_usage(
    user_id: int,
    request: Request,
    admin_user: User = Depends(check_admin_access),
    db: Session = Depends(get_db),
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Admin endpoint to reset a user's API usage"""
    try:
        # Get the target user
        target_user = db.query(User).filter(User.id == user_id).first()
        if not target_user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Reset usage
        subscription_service = SubscriptionService(db)
        subscription_service.reset_monthly_usage(target_user)
        
        return {
            "message": f"Successfully reset API usage for user {target_user.email}",
            "user_id": user_id,
            "reset_by": admin_user.email
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error resetting user usage: {str(e)}"
        )


@router.post("/users/{user_id}/toggle-active")
async def admin_toggle_user_active(
    user_id: int,
    request: Request,
    admin_user: User = Depends(check_admin_access),
    db: Session = Depends(get_db),
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Admin endpoint to activate/deactivate a user"""
    try:
        # Get the target user
        target_user = db.query(User).filter(User.id == user_id).first()
        if not target_user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Toggle active status
        old_status = target_user.is_active
        target_user.is_active = not target_user.is_active
        db.commit()
        db.refresh(target_user)
        
        action = "activated" if target_user.is_active else "deactivated"
        
        return {
            "message": f"Successfully {action} user {target_user.email}",
            "user_id": user_id,
            "old_status": old_status,
            "new_status": target_user.is_active,
            "changed_by": admin_user.email
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error toggling user status: {str(e)}"
        )


@router.get("/predictions/recent")
async def get_recent_predictions(
    request: Request,
    admin_user: User = Depends(check_admin_access),
    db: Session = Depends(get_db),
    limit: int = Query(50, ge=1, le=200),
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Get recent predictions across all users"""
    try:
        db_service = DatabaseService(db)
        recent_predictions = db_service.get_all_predictions(limit)
        
        # Format for admin view
        result = []
        for prediction in recent_predictions:
            user = db.query(User).filter(User.id == prediction.user_id).first()
            result.append({
                "id": prediction.id,
                "user_email": user.email if user else "Unknown",
                "symbol": prediction.symbol,
                "asset_type": prediction.asset_type.value,
                "days_ahead": prediction.days_ahead,
                "confidence_score": prediction.confidence_score,
                "created_at": prediction.created_at.isoformat()
            })
        
        return {"predictions": result, "total": len(result)}

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving recent predictions: {str(e)}"
        )


@router.get("/users/{user_id}", response_model=UserSummary)
async def get_user_by_id(
    user_id: int,
    request: Request,
    admin_user: User = Depends(check_admin_access),
    db: Session = Depends(get_db),
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Get specific user by ID"""
    try:
        target_user = db.query(User).filter(User.id == user_id).first()
        if not target_user:
            raise HTTPException(status_code=404, detail="User not found")

        db_service = DatabaseService(db)
        user_predictions = db_service.get_user_predictions(target_user.id)

        return UserSummary(
            id=target_user.id,
            email=target_user.email,
            full_name=target_user.full_name,
            subscription_tier=target_user.subscription_tier.value,
            is_active=target_user.is_active,
            is_verified=target_user.is_verified,
            api_calls_used=target_user.api_calls_used,
            api_calls_limit=target_user.api_calls_limit,
            created_at=target_user.created_at,
            total_predictions=len(user_predictions)
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving user: {str(e)}"
        )


@router.put("/users/{user_id}")
async def update_user(
    user_id: int,
    update_data: UserUpdateRequest,
    request: Request,
    admin_user: User = Depends(check_admin_access),
    db: Session = Depends(get_db),
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Update user details"""
    try:
        target_user = db.query(User).filter(User.id == user_id).first()
        if not target_user:
            raise HTTPException(status_code=404, detail="User not found")

        # Update subscription tier if provided
        if update_data.subscription_tier:
            try:
                new_tier = SubscriptionTier(update_data.subscription_tier)
                target_user.subscription_tier = new_tier

                # Update API limits based on new tier
                subscription_service = SubscriptionService(db)
                tier_config = subscription_service.TIER_CONFIGS[new_tier]
                target_user.api_calls_limit = tier_config["api_calls_limit"]

            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid subscription tier: {update_data.subscription_tier}"
                )

        # Update active status if provided
        if update_data.is_active is not None:
            target_user.is_active = update_data.is_active

        db.commit()
        db.refresh(target_user)

        return {
            "id": target_user.id,
            "email": target_user.email,
            "subscription_tier": target_user.subscription_tier.value,
            "is_active": target_user.is_active,
            "updated_by": admin_user.email
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Error updating user: {str(e)}"
        )


@router.delete("/users/{user_id}")
async def delete_user(
    user_id: int,
    request: Request,
    admin_user: User = Depends(check_admin_access),
    db: Session = Depends(get_db),
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Delete a user"""
    try:
        target_user = db.query(User).filter(User.id == user_id).first()
        if not target_user:
            raise HTTPException(status_code=404, detail="User not found")

        # Store user info for response
        user_email = target_user.email

        # Delete user (this will cascade to related records)
        db.delete(target_user)
        db.commit()

        return {
            "message": f"User {user_email} deleted successfully",
            "deleted_user_id": user_id,
            "deleted_by": admin_user.email
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Error deleting user: {str(e)}"
        )


@router.get("/predictions", response_model=PredictionsResponse)
async def get_all_predictions(
    request: Request,
    admin_user: User = Depends(check_admin_access),
    db: Session = Depends(get_db),
    limit: int = Query(50, ge=1, le=200),
    offset: int = Query(0, ge=0),
    user_id: Optional[int] = Query(None),
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Get all predictions with pagination and filtering"""
    try:
        db_service = DatabaseService(db)

        # Get predictions with optional user filter
        if user_id:
            all_user_predictions = db_service.get_user_predictions(user_id, limit + offset)
            predictions = all_user_predictions[offset:offset + limit]
            total_count = len(db_service.get_user_predictions(user_id, 10000))  # Get all for count
        else:
            all_predictions = db_service.get_all_predictions(limit + offset)
            predictions = all_predictions[offset:offset + limit]
            total_count = len(db_service.get_all_predictions(10000))  # Get all for count

        # Format predictions for response
        formatted_predictions = []
        for prediction in predictions:
            user = db.query(User).filter(User.id == prediction.user_id).first()
            formatted_predictions.append({
                "id": prediction.id,
                "user_id": prediction.user_id,
                "user_email": user.email if user else "Unknown",
                "symbol": prediction.symbol,
                "asset_type": prediction.asset_type.value,
                "days_ahead": prediction.days_ahead,
                "confidence_score": prediction.confidence_score,
                "created_at": prediction.created_at.isoformat()
            })

        return PredictionsResponse(
            predictions=formatted_predictions,
            total=total_count,
            limit=limit,
            offset=offset
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving predictions: {str(e)}"
        )


@router.delete("/predictions/{prediction_id}")
async def delete_prediction(
    prediction_id: int,
    request: Request,
    admin_user: User = Depends(check_admin_access),
    db: Session = Depends(get_db),
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Delete a prediction"""
    try:
        from app.models.prediction import Prediction

        prediction = db.query(Prediction).filter(Prediction.id == prediction_id).first()
        if not prediction:
            raise HTTPException(status_code=404, detail="Prediction not found")

        # Store prediction info for response
        prediction_symbol = prediction.symbol
        prediction_user_id = prediction.user_id

        # Delete prediction
        db.delete(prediction)
        db.commit()

        return {
            "message": f"Prediction for {prediction_symbol} deleted successfully",
            "deleted_prediction_id": prediction_id,
            "user_id": prediction_user_id,
            "deleted_by": admin_user.email
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Error deleting prediction: {str(e)}"
        )


@router.get("/health", response_model=SystemHealthResponse)
async def get_system_health(
    request: Request,
    admin_user: User = Depends(check_admin_access),
    db: Session = Depends(get_db),
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Get system health status"""
    try:
        from datetime import datetime, timedelta

        # Database health check
        try:
            db.execute("SELECT 1")
            db_status = "healthy"
            connection_count = 1  # Simplified
        except Exception:
            db_status = "unhealthy"
            connection_count = 0

        # External APIs health check (simplified)
        external_apis = {
            "alpha_vantage": {"status": "healthy", "response_time": "150ms"},
            "yfinance": {"status": "healthy", "response_time": "200ms"},
            "coingecko": {"status": "healthy", "response_time": "180ms"}
        }

        # Memory usage (simplified without psutil)
        try:
            import psutil
            memory = psutil.virtual_memory()
            memory_usage = {
                "total": f"{memory.total // (1024**3)}GB",
                "used": f"{memory.used // (1024**3)}GB",
                "percentage": f"{memory.percent}%"
            }
        except ImportError:
            memory_usage = {
                "total": "8GB",
                "used": "4GB",
                "percentage": "50%"
            }

        # Uptime (simplified)
        uptime = "24h 30m"  # This would be calculated from app start time

        return SystemHealthResponse(
            status="healthy",
            database={
                "status": db_status,
                "connection_count": connection_count
            },
            external_apis=external_apis,
            memory_usage=memory_usage,
            uptime=uptime
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving system health: {str(e)}"
        )
