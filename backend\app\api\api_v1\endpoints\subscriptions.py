"""
Subscription management endpoints
"""

from typing import Dict, List
from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import get_current_active_user
from app.core.rate_limiter import rate_limit_general, add_rate_limit_headers
from app.models.user import User, SubscriptionTier
from app.services.subscription_service import SubscriptionService

router = APIRouter()


class SubscriptionUpgradeRequest(BaseModel):
    """Request model for subscription upgrade"""
    new_tier: SubscriptionTier
    payment_method_id: str  # Stripe payment method ID or similar


class UsageStatsResponse(BaseModel):
    """Response model for usage statistics"""
    subscription_tier: str
    api_calls_used: int
    api_calls_limit: int
    api_usage_percent: float
    daily_predictions_used: int
    daily_predictions_limit: int
    daily_usage_percent: float
    max_days_ahead: int
    features: List[str]
    price: float


class SubscriptionTierInfo(BaseModel):
    """Information about a subscription tier"""
    name: str
    price: float
    api_calls_limit: int
    predictions_per_day: int
    max_days_ahead: int
    features: List[str]


class AllTiersResponse(BaseModel):
    """Response model for all subscription tiers"""
    tiers: Dict[str, SubscriptionTierInfo]


@router.get("/usage", response_model=UsageStatsResponse)
async def get_usage_stats(
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Get current user's usage statistics"""
    try:
        subscription_service = SubscriptionService(db)
        stats = subscription_service.get_usage_stats(current_user)
        
        return UsageStatsResponse(**stats)
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving usage statistics: {str(e)}"
        )


@router.get("/tiers", response_model=AllTiersResponse)
async def get_subscription_tiers(
    request: Request,
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Get information about all available subscription tiers"""
    try:
        subscription_service = SubscriptionService(None)  # No DB needed for static data
        tiers_data = subscription_service.get_all_tiers()
        
        # Convert to response format
        tiers = {}
        for tier_name, tier_info in tiers_data.items():
            tiers[tier_name] = SubscriptionTierInfo(**tier_info)
        
        return AllTiersResponse(tiers=tiers)
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving subscription tiers: {str(e)}"
        )


@router.post("/upgrade")
async def upgrade_subscription(
    request: Request,
    upgrade_request: SubscriptionUpgradeRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Upgrade user's subscription tier"""
    try:
        subscription_service = SubscriptionService(db)
        
        # Validate the upgrade request
        if upgrade_request.new_tier.value <= current_user.subscription_tier.value:
            raise HTTPException(
                status_code=400,
                detail="Cannot downgrade or set same tier. Contact support for downgrades."
            )
        
        # TODO: Integrate with payment processor (Stripe, PayPal, etc.)
        # For now, we'll simulate successful payment
        payment_successful = await process_payment(
            upgrade_request.payment_method_id,
            upgrade_request.new_tier,
            current_user
        )
        
        if not payment_successful:
            raise HTTPException(
                status_code=402,
                detail="Payment failed. Please check your payment method and try again."
            )
        
        # Upgrade the subscription
        updated_user = subscription_service.upgrade_subscription(current_user, upgrade_request.new_tier)
        
        return {
            "message": f"Successfully upgraded to {upgrade_request.new_tier.value}",
            "new_tier": updated_user.subscription_tier.value,
            "effective_date": updated_user.updated_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error upgrading subscription: {str(e)}"
        )


@router.post("/downgrade")
async def request_downgrade(
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Request subscription downgrade (requires manual approval)"""
    try:
        # TODO: Implement downgrade request system
        # This would typically create a support ticket or send an email
        
        return {
            "message": "Downgrade request submitted. Our support team will contact you within 24 hours.",
            "current_tier": current_user.subscription_tier.value,
            "support_email": "<EMAIL>"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing downgrade request: {str(e)}"
        )


@router.get("/billing-history")
async def get_billing_history(
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Get user's billing history"""
    try:
        # TODO: Implement billing history retrieval
        # This would integrate with payment processor to get transaction history
        
        return {
            "message": "Billing history feature coming soon",
            "current_tier": current_user.subscription_tier.value,
            "billing_email": current_user.email
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving billing history: {str(e)}"
        )


@router.post("/cancel")
async def cancel_subscription(
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    _rate_limit: bool = Depends(rate_limit_general)
):
    """Cancel subscription (downgrades to free tier at end of billing period)"""
    try:
        # TODO: Implement subscription cancellation
        # This would typically:
        # 1. Cancel recurring billing
        # 2. Set cancellation date
        # 3. Allow access until end of billing period
        # 4. Send confirmation email
        
        return {
            "message": "Subscription cancellation processed. You will retain access until the end of your billing period.",
            "current_tier": current_user.subscription_tier.value,
            "access_until": "End of current billing period",
            "support_email": "<EMAIL>"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error canceling subscription: {str(e)}"
        )


async def process_payment(payment_method_id: str, tier: SubscriptionTier, user: User) -> bool:
    """
    Process payment for subscription upgrade
    
    This is a placeholder function. In a real implementation, this would:
    1. Integrate with Stripe, PayPal, or other payment processor
    2. Create payment intent
    3. Process the payment
    4. Handle webhooks for payment confirmation
    5. Update subscription status
    
    Args:
        payment_method_id: Payment method identifier
        tier: New subscription tier
        user: User object
    
    Returns:
        True if payment successful, False otherwise
    """
    # TODO: Implement actual payment processing
    # For demo purposes, we'll simulate successful payment
    
    # Simulate payment processing delay
    import asyncio
    await asyncio.sleep(0.1)
    
    # In real implementation, you would:
    # 1. Validate payment method
    # 2. Calculate amount based on tier
    # 3. Process payment with payment provider
    # 4. Handle errors and retries
    # 5. Store transaction record
    
    # For now, always return success (except for demo failure case)
    if payment_method_id == "fail_for_demo":
        return False
    
    return True


# Note: Rate limit headers are added by the rate_limit dependencies automatically
