import React, { useState } from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  TextField,
  Button,
  Box,
  Card,
  CardContent,
  Chip,
  LinearProgress,
  Avatar,
  Divider,
  Alert,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Person,
  Email,
  Security,
  Notifications,
  CreditCard,
  Assessment,
  TrendingUp,
  Edit,
  Save,
  Cancel,
} from '@mui/icons-material';

const Profile: React.FC = () => {
  const [editMode, setEditMode] = useState(false);
  const [userInfo, setUserInfo] = useState({
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+****************',
  });
  const [notifications, setNotifications] = useState({
    emailAlerts: true,
    pushNotifications: false,
    weeklyReports: true,
  });

  // Mock user stats
  const userStats = {
    totalPredictions: 156,
    accuracy: 87.5,
    apiCallsUsed: 45,
    apiCallsLimit: 100,
    subscriptionPlan: 'Free',
    memberSince: 'January 2024',
  };

  const handleSave = () => {
    // TODO: Implement save functionality
    setEditMode(false);
  };

  const handleCancel = () => {
    // TODO: Reset form data
    setEditMode(false);
  };

  const handleNotificationChange = (setting: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setNotifications({
      ...notifications,
      [setting]: event.target.checked,
    });
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Profile Settings
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Manage your account settings and preferences
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* User Info Card */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, background: 'linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Avatar sx={{ width: 80, height: 80, bgcolor: 'primary.main', mr: 2 }}>
                <Person sx={{ fontSize: 40 }} />
              </Avatar>
              <Box>
                <Typography variant="h6">
                  {userInfo.firstName} {userInfo.lastName}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {userInfo.email}
                </Typography>
                <Chip
                  label={userStats.subscriptionPlan}
                  color="primary"
                  size="small"
                  sx={{ mt: 1 }}
                />
              </Box>
            </Box>

            <Divider sx={{ mb: 3 }} />

            {/* User Stats */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Account Statistics
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">Total Predictions</Typography>
                <Typography variant="body2" fontWeight="bold">
                  {userStats.totalPredictions}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">Accuracy Rate</Typography>
                <Typography variant="body2" fontWeight="bold" color="success.main">
                  {userStats.accuracy}%
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">Member Since</Typography>
                <Typography variant="body2" fontWeight="bold">
                  {userStats.memberSince}
                </Typography>
              </Box>
            </Box>

            {/* API Usage */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                API Usage This Month
              </Typography>
              <LinearProgress
                variant="determinate"
                value={(userStats.apiCallsUsed / userStats.apiCallsLimit) * 100}
                sx={{ mb: 1 }}
              />
              <Typography variant="body2" color="text.secondary">
                {userStats.apiCallsUsed} / {userStats.apiCallsLimit} calls used
              </Typography>
            </Box>

            <Button
              variant="outlined"
              fullWidth
              startIcon={<TrendingUp />}
              sx={{ mb: 2 }}
            >
              Upgrade Plan
            </Button>
          </Paper>
        </Grid>

        {/* Settings */}
        <Grid item xs={12} md={8}>
          <Grid container spacing={3}>
            {/* Personal Information */}
            <Grid item xs={12}>
              <Paper sx={{ p: 3, background: 'linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
                    <Person sx={{ mr: 1 }} />
                    Personal Information
                  </Typography>
                  <Button
                    startIcon={editMode ? <Save /> : <Edit />}
                    onClick={editMode ? handleSave : () => setEditMode(true)}
                    variant={editMode ? 'contained' : 'outlined'}
                  >
                    {editMode ? 'Save' : 'Edit'}
                  </Button>
                </Box>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="First Name"
                      value={userInfo.firstName}
                      disabled={!editMode}
                      onChange={(e) => setUserInfo({ ...userInfo, firstName: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Last Name"
                      value={userInfo.lastName}
                      disabled={!editMode}
                      onChange={(e) => setUserInfo({ ...userInfo, lastName: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Email"
                      value={userInfo.email}
                      disabled={!editMode}
                      onChange={(e) => setUserInfo({ ...userInfo, email: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Phone"
                      value={userInfo.phone}
                      disabled={!editMode}
                      onChange={(e) => setUserInfo({ ...userInfo, phone: e.target.value })}
                    />
                  </Grid>
                </Grid>

                {editMode && (
                  <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
                    <Button
                      variant="outlined"
                      startIcon={<Cancel />}
                      onClick={handleCancel}
                    >
                      Cancel
                    </Button>
                  </Box>
                )}
              </Paper>
            </Grid>

            {/* Notification Settings */}
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, background: 'linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)' }}>
                <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Notifications sx={{ mr: 1 }} />
                  Notification Settings
                </Typography>

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notifications.emailAlerts}
                        onChange={handleNotificationChange('emailAlerts')}
                      />
                    }
                    label="Email Alerts"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notifications.pushNotifications}
                        onChange={handleNotificationChange('pushNotifications')}
                      />
                    }
                    label="Push Notifications"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={notifications.weeklyReports}
                        onChange={handleNotificationChange('weeklyReports')}
                      />
                    }
                    label="Weekly Reports"
                  />
                </Box>
              </Paper>
            </Grid>

            {/* Security Settings */}
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, background: 'linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)' }}>
                <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Security sx={{ mr: 1 }} />
                  Security
                </Typography>

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Button variant="outlined" fullWidth>
                    Change Password
                  </Button>
                  <Button variant="outlined" fullWidth>
                    Enable Two-Factor Authentication
                  </Button>
                  <Button variant="outlined" fullWidth>
                    Download Account Data
                  </Button>
                </Box>
              </Paper>
            </Grid>

            {/* Subscription Management */}
            <Grid item xs={12}>
              <Paper sx={{ p: 3, background: 'linear-gradient(135deg, #1a1d3a 0%, #2a2d5a 100%)' }}>
                <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <CreditCard sx={{ mr: 1 }} />
                  Subscription Management
                </Typography>

                <Alert severity="info" sx={{ mb: 3 }}>
                  You are currently on the <strong>Free Plan</strong>. Upgrade to unlock more features and higher API limits.
                </Alert>

                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <Card sx={{ background: 'rgba(25, 118, 210, 0.1)', border: '2px solid', borderColor: 'primary.main' }}>
                      <CardContent>
                        <Typography variant="h6" color="primary">Free</Typography>
                        <Typography variant="h4">$0</Typography>
                        <Typography variant="body2" color="text.secondary">per month</Typography>
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="body2">• 100 predictions/month</Typography>
                          <Typography variant="body2">• Basic support</Typography>
                          <Typography variant="body2">• Standard accuracy</Typography>
                        </Box>
                        <Button variant="outlined" fullWidth sx={{ mt: 2 }} disabled>
                          Current Plan
                        </Button>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Card sx={{ background: 'rgba(76, 175, 80, 0.1)' }}>
                      <CardContent>
                        <Typography variant="h6" color="success.main">Pro</Typography>
                        <Typography variant="h4">$29</Typography>
                        <Typography variant="body2" color="text.secondary">per month</Typography>
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="body2">• 1,000 predictions/month</Typography>
                          <Typography variant="body2">• Priority support</Typography>
                          <Typography variant="body2">• Enhanced accuracy</Typography>
                        </Box>
                        <Button variant="contained" color="success" fullWidth sx={{ mt: 2 }}>
                          Upgrade
                        </Button>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Card sx={{ background: 'rgba(156, 39, 176, 0.1)' }}>
                      <CardContent>
                        <Typography variant="h6" color="secondary.main">Enterprise</Typography>
                        <Typography variant="h4">$99</Typography>
                        <Typography variant="body2" color="text.secondary">per month</Typography>
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="body2">• Unlimited predictions</Typography>
                          <Typography variant="body2">• 24/7 support</Typography>
                          <Typography variant="body2">• Custom models</Typography>
                        </Box>
                        <Button variant="contained" color="secondary" fullWidth sx={{ mt: 2 }}>
                          Contact Sales
                        </Button>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Paper>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Profile;
