"""
Stock prediction endpoints
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query, Request, Response
from pydantic import BaseModel
from datetime import datetime, date
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import get_current_active_user
from app.core.rate_limiter import rate_limit_predictions, subscription_check, add_rate_limit_headers
from app.models.user import User
from app.services.stock_service import stock_service

router = APIRouter()


class StockPredictionRequest(BaseModel):
    """Stock prediction request model"""
    symbol: str
    days_ahead: int = 30
    include_technical_indicators: bool = True


class StockPredictionResponse(BaseModel):
    """Stock prediction response model"""
    symbol: str
    current_price: float
    predicted_prices: List[float]
    prediction_dates: List[date]
    confidence_score: float
    model_version: str
    created_at: datetime


class StockDataResponse(BaseModel):
    """Stock data response model"""
    symbol: str
    name: str
    current_price: float
    change_percent: float
    volume: int
    market_cap: Optional[float]
    last_updated: datetime


@router.get("/search", response_model=List[dict])
async def search_stocks(
    query: str = Query(..., min_length=1, description="Stock symbol or company name"),
    limit: int = Query(10, ge=1, le=50, description="Number of results to return"),
    current_user: User = Depends(get_current_active_user)
):
    """Search for stocks by symbol or company name"""
    results = await stock_service.search_stocks(query, limit)
    return results


@router.get("/{symbol}/data", response_model=StockDataResponse)
async def get_stock_data(
    symbol: str,
    current_user: User = Depends(get_current_active_user)
):
    """Get current stock data"""
    stock_data = await stock_service.get_stock_data(symbol)
    if not stock_data:
        raise HTTPException(status_code=404, detail="Stock not found")

    return StockDataResponse(**stock_data)


@router.post("/{symbol}/predict", response_model=StockPredictionResponse)
async def predict_stock_price(
    symbol: str,
    request: StockPredictionRequest,
    http_request: Request,
    response: Response,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    _rate_limit: bool = Depends(rate_limit_predictions)
):
    """Generate stock price predictions using LSTM model"""
    # Validate input parameters
    if not symbol or len(symbol.strip()) == 0:
        raise HTTPException(status_code=422, detail="Symbol cannot be empty")

    if request.days_ahead <= 0 or request.days_ahead > 365:
        raise HTTPException(status_code=422, detail="days_ahead must be between 1 and 365")

    symbol = symbol.upper().strip()

    # Validate subscription access with actual request parameters
    from app.services.subscription_service import SubscriptionService
    subscription_service = SubscriptionService(db)
    subscription_service.validate_subscription_access(current_user, "prediction", days_ahead=request.days_ahead)

    try:
        # Add rate limit headers to response
        rate_limit_headers = add_rate_limit_headers(http_request, current_user)
        for header, value in rate_limit_headers.items():
            response.headers[header] = value

        # Use the enhanced stock service with LSTM prediction and database integration
        prediction_result = await stock_service.predict_stock_price(
            symbol=symbol,
            days_ahead=request.days_ahead,
            user_id=current_user.id,
            db=db
        )

        # Convert string dates to date objects for response model
        prediction_dates = [
            datetime.strptime(date_str, "%Y-%m-%d").date()
            for date_str in prediction_result["prediction_dates"]
        ]

        return StockPredictionResponse(
            symbol=prediction_result["symbol"],
            current_price=prediction_result["current_price"],
            predicted_prices=prediction_result["predicted_prices"],
            prediction_dates=prediction_dates,
            confidence_score=prediction_result["confidence_score"],
            model_version=prediction_result["model_version"],
            created_at=prediction_result["created_at"]
        )

    except ValueError as e:
        error_msg = str(e).lower()
        if "no data found" in error_msg or "invalid symbol" in error_msg or "delisted" in error_msg:
            raise HTTPException(status_code=404, detail=f"Stock symbol '{symbol}' not found or delisted")
        elif "insufficient data" in error_msg or "minimum" in error_msg:
            raise HTTPException(status_code=400, detail=f"Insufficient data for symbol '{symbol}' to generate prediction")
        else:
            raise HTTPException(status_code=422, detail=f"Invalid input: {str(e)}")
    except Exception as e:
        error_msg = str(e).lower()
        if "no data found" in error_msg or "symbol may be delisted" in error_msg:
            raise HTTPException(status_code=404, detail=f"Stock symbol '{symbol}' not found or delisted")
        raise HTTPException(
            status_code=500,
            detail=f"Error generating prediction for {symbol}: {str(e)}"
        )


@router.post("/{symbol}/train")
async def train_model(
    symbol: str,
    force_retrain: bool = False,
    current_user: User = Depends(get_current_active_user)
):
    """Train LSTM model for a specific stock symbol"""
    try:
        from app.ml.stocks.predictor import stock_predictor

        training_result = await stock_predictor.train_model(
            symbol=symbol,
            force_retrain=force_retrain
        )

        return training_result

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error training model for {symbol}: {str(e)}"
        )


@router.get("/{symbol}/model-info")
async def get_model_info(
    symbol: str,
    current_user: User = Depends(get_current_active_user)
):
    """Get information about the LSTM model for a stock symbol"""
    try:
        from app.ml.stocks.predictor import stock_predictor

        model_info = stock_predictor.get_model_info(symbol)
        return model_info

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting model info for {symbol}: {str(e)}"
        )


@router.get("/{symbol}/history")
async def get_stock_history(
    symbol: str,
    start_date: Optional[date] = Query(None, description="Start date for historical data"),
    end_date: Optional[date] = Query(None, description="End date for historical data"),
    interval: str = Query("1d", description="Data interval (1d, 1h, etc.)"),
    current_user: User = Depends(get_current_active_user)
):
    """Get historical stock data"""
    start_datetime = datetime.combine(start_date, datetime.min.time()) if start_date else None
    end_datetime = datetime.combine(end_date, datetime.min.time()) if end_date else None

    historical_data = await stock_service.get_historical_data(
        symbol, start_datetime, end_datetime, interval
    )
    return historical_data
