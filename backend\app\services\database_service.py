"""
Database service for handling database operations
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

from app.core.database import get_db
from app.models import (
    User, Prediction, PredictionAccuracy, MLModel, TrainingHistory, 
    PredictionCache, AssetType, PredictionStatus, ModelType, ModelStatus
)


class DatabaseService:
    """Service for database operations"""
    
    def __init__(self, db: Session):
        self.db = db
    
    # User operations
    def create_user(self, email: str, hashed_password: str, full_name: Optional[str] = None) -> User:
        """Create a new user"""
        user = User(
            email=email,
            hashed_password=hashed_password,
            full_name=full_name
        )
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        return user
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        return self.db.query(User).filter(User.email == email).first()
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID"""
        return self.db.query(User).filter(User.id == user_id).first()
    
    # Prediction operations
    def save_prediction(self, user_id: int, symbol: str, asset_type: AssetType, 
                       prediction_data: Dict[str, Any]) -> Prediction:
        """Save a prediction to database"""
        prediction = Prediction(
            user_id=user_id,
            symbol=symbol.upper(),
            asset_type=asset_type,
            days_ahead=prediction_data.get("days_ahead", 30),
            current_price=prediction_data.get("current_price"),
            predicted_prices=prediction_data.get("predicted_prices"),
            prediction_dates=prediction_data.get("prediction_dates"),
            confidence_score=prediction_data.get("confidence_score"),
            model_version=prediction_data.get("model_version", "unknown"),
            status=PredictionStatus.COMPLETED
        )
        self.db.add(prediction)
        self.db.commit()
        self.db.refresh(prediction)
        return prediction
    
    def get_user_predictions(self, user_id: int, limit: int = 50) -> List[Prediction]:
        """Get user's recent predictions"""
        return (self.db.query(Prediction)
                .filter(Prediction.user_id == user_id)
                .order_by(desc(Prediction.created_at))
                .limit(limit)
                .all())
    
    def get_predictions_by_symbol(self, symbol: str, limit: int = 20) -> List[Prediction]:
        """Get recent predictions for a symbol"""
        return (self.db.query(Prediction)
                .filter(Prediction.symbol == symbol.upper())
                .order_by(desc(Prediction.created_at))
                .limit(limit)
                .all())
    
    # ML Model operations
    def save_model_metadata(self, symbol: str, model_type: ModelType, 
                           model_data: Dict[str, Any]) -> MLModel:
        """Save ML model metadata"""
        model = MLModel(
            symbol=symbol.upper(),
            model_type=model_type,
            model_version=model_data.get("model_version", "v1.0.0"),
            status=ModelStatus.TRAINED,
            hyperparameters=model_data.get("hyperparameters"),
            training_config=model_data.get("training_config"),
            training_loss=model_data.get("training_loss"),
            validation_loss=model_data.get("validation_loss"),
            accuracy_score=model_data.get("accuracy_score"),
            model_file_path=model_data.get("model_file_path"),
            training_data_size=model_data.get("training_data_size"),
            training_duration_seconds=model_data.get("training_duration_seconds"),
            epochs_completed=model_data.get("epochs_completed"),
            last_trained_at=datetime.now()
        )
        self.db.add(model)
        self.db.commit()
        self.db.refresh(model)
        return model
    
    def get_active_model(self, symbol: str, model_type: ModelType) -> Optional[MLModel]:
        """Get the active model for a symbol and type"""
        return (self.db.query(MLModel)
                .filter(and_(
                    MLModel.symbol == symbol.upper(),
                    MLModel.model_type == model_type,
                    MLModel.is_active == True,
                    MLModel.status == ModelStatus.TRAINED
                ))
                .order_by(desc(MLModel.last_trained_at))
                .first())
    
    def get_models_by_symbol(self, symbol: str) -> List[MLModel]:
        """Get all models for a symbol"""
        return (self.db.query(MLModel)
                .filter(MLModel.symbol == symbol.upper())
                .order_by(desc(MLModel.created_at))
                .all())
    
    # Training history operations
    def save_training_history(self, model_id: int, symbol: str, model_type: ModelType,
                             training_data: Dict[str, Any]) -> TrainingHistory:
        """Save training history"""
        history = TrainingHistory(
            model_id=model_id,
            symbol=symbol.upper(),
            model_type=model_type,
            training_start=training_data.get("training_start", datetime.now()),
            training_end=training_data.get("training_end"),
            training_duration_seconds=training_data.get("training_duration_seconds"),
            data_start_date=training_data.get("data_start_date"),
            data_end_date=training_data.get("data_end_date"),
            training_samples=training_data.get("training_samples"),
            validation_samples=training_data.get("validation_samples"),
            final_training_loss=training_data.get("final_training_loss"),
            final_validation_loss=training_data.get("final_validation_loss"),
            best_epoch=training_data.get("best_epoch"),
            total_epochs=training_data.get("total_epochs"),
            hyperparameters=training_data.get("hyperparameters"),
            training_config=training_data.get("training_config"),
            status=training_data.get("status", ModelStatus.TRAINED),
            error_message=training_data.get("error_message"),
            notes=training_data.get("notes")
        )
        self.db.add(history)
        self.db.commit()
        self.db.refresh(history)
        return history
    
    def get_training_history(self, symbol: str, limit: int = 10) -> List[TrainingHistory]:
        """Get training history for a symbol"""
        return (self.db.query(TrainingHistory)
                .filter(TrainingHistory.symbol == symbol.upper())
                .order_by(desc(TrainingHistory.created_at))
                .limit(limit)
                .all())
    
    # Prediction cache operations
    def save_prediction_cache(self, symbol: str, model_type: ModelType, 
                             cache_data: Dict[str, Any]) -> PredictionCache:
        """Save prediction to cache"""
        cache_key = f"{symbol}_{model_type.value}_{cache_data.get('days_ahead', 30)}"
        expires_at = datetime.now() + timedelta(minutes=cache_data.get("cache_ttl_minutes", 5))
        
        cache = PredictionCache(
            symbol=symbol.upper(),
            model_type=model_type,
            model_version=cache_data.get("model_version", "unknown"),
            cache_key=cache_key,
            prediction_data=cache_data.get("prediction_data"),
            days_ahead=cache_data.get("days_ahead", 30),
            confidence_score=cache_data.get("confidence_score"),
            expires_at=expires_at
        )
        self.db.add(cache)
        self.db.commit()
        self.db.refresh(cache)
        return cache
    
    def get_cached_prediction(self, symbol: str, model_type: ModelType, 
                             days_ahead: int = 30) -> Optional[PredictionCache]:
        """Get cached prediction if valid"""
        cache_key = f"{symbol.upper()}_{model_type.value}_{days_ahead}"
        cache = (self.db.query(PredictionCache)
                .filter(and_(
                    PredictionCache.cache_key == cache_key,
                    PredictionCache.expires_at > datetime.now()
                ))
                .first())
        
        if cache:
            # Update access tracking
            cache.access_count += 1
            cache.last_accessed = datetime.now()
            self.db.commit()
        
        return cache
    
    def cleanup_expired_cache(self) -> int:
        """Clean up expired cache entries"""
        deleted = (self.db.query(PredictionCache)
                  .filter(PredictionCache.expires_at <= datetime.now())
                  .delete())
        self.db.commit()
        return deleted

    def get_predictions_by_symbol(self, symbol: str, limit: int = 50) -> List[Prediction]:
        """Get predictions for a specific symbol"""
        return self.db.query(Prediction).filter(
            Prediction.symbol == symbol.upper()
        ).order_by(Prediction.created_at.desc()).limit(limit).all()

    def get_all_predictions(self, limit: int = 1000) -> List[Prediction]:
        """Get all predictions across all users"""
        return self.db.query(Prediction).order_by(
            Prediction.created_at.desc()
        ).limit(limit).all()


def get_database_service(db: Session = None) -> DatabaseService:
    """Get database service instance"""
    if db is None:
        db = next(get_db())
    return DatabaseService(db)
