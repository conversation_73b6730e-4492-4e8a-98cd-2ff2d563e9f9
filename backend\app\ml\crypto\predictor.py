"""
Cryptocurrency Price Predictor using LSTM

This module provides the main interface for cryptocurrency price prediction,
combining data preprocessing, LSTM model training, and prediction generation
with crypto-specific optimizations.
"""

import os
import asyncio
import numpy as np
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import logging

from app.ml.crypto.data_preprocessor import CryptoDataPreprocessor
from app.ml.crypto.lstm_model import CryptoLSTMModel
from app.services.crypto_service import crypto_service

logger = logging.getLogger(__name__)


class CryptoPredictor:
    """
    Main cryptocurrency prediction class that orchestrates data preprocessing,
    model training, and prediction generation for crypto assets.
    """

    def __init__(self):
        """Initialize the crypto predictor"""
        self.preprocessor = CryptoDataPreprocessor(sequence_length=60, prediction_days=7)
        self.model = None
        self.symbol = None
        self.model_dir = "models/crypto"
        self.last_training_data_date = None

        # Ensure model directory exists
        os.makedirs(self.model_dir, exist_ok=True)

    async def predict_prices(self, symbol: str, days_ahead: int = 30) -> Dict:
        """
        Predict cryptocurrency prices for the specified number of days

        Args:
            symbol: Cryptocurrency symbol (e.g., 'BTC', 'ETH')
            days_ahead: Number of days to predict ahead

        Returns:
            Dictionary containing predictions and metadata
        """
        try:
            logger.info(f"Generating crypto prediction for {symbol}, {days_ahead} days ahead")

            # Load or train model
            if self.model is None or self.symbol != symbol:
                model_loaded = await self.load_model(symbol)
                if not model_loaded:
                    logger.info(f"No existing model found for {symbol}, training new model")
                    training_result = await self.train_model(symbol)
                    if not training_result.get('success', False):
                        logger.warning(f"Training failed for {symbol}, using fallback prediction")
                        return await self._generate_fallback_prediction(symbol, days_ahead)

            # Check if model needs retraining (crypto markets change quickly)
            if await self._should_retrain_model(symbol):
                logger.info(f"Model for {symbol} needs retraining")
                await self.train_model(symbol, force_retrain=True)

            # Get recent data for prediction
            historical_data = await crypto_service.get_historical_data(
                symbol=symbol,
                start_date=datetime.now() - timedelta(days=90),
                end_date=datetime.now()
            )

            if not historical_data.get('data'):
                logger.warning(f"No historical data available for {symbol}")
                return await self._generate_fallback_prediction(symbol, days_ahead)

            # Preprocess data
            processed_data, df = self.preprocessor.preprocess_crypto_data(historical_data['data'])

            if len(processed_data) < self.preprocessor.sequence_length:
                logger.warning(f"Insufficient data for {symbol} prediction")
                return await self._generate_fallback_prediction(symbol, days_ahead)

            # Generate predictions
            predictions = []
            confidence_scores = []

            # Use the last sequence for prediction
            current_sequence = processed_data[-self.preprocessor.sequence_length:].reshape(1, -1, processed_data.shape[1])

            for day in range(days_ahead):
                # Predict next value
                pred = self.model.predict(current_sequence)

                # Denormalize prediction (assuming first column is close price)
                denormalized_pred = self.preprocessor.denormalize_data(pred, 'crypto_features')[0]
                predictions.append(float(denormalized_pred))

                # Calculate confidence based on crypto volatility
                confidence = self._calculate_crypto_confidence(processed_data, pred, day)
                confidence_scores.append(confidence)

                # Update sequence for next prediction (simplified approach)
                # In practice, you'd want to update with the predicted values
                current_sequence = np.roll(current_sequence, -1, axis=1)
                current_sequence[0, -1, 0] = pred[0]  # Update close price

            # Get current price
            current_data = await crypto_service.get_crypto_data(symbol)
            current_price = current_data.get('current_price', predictions[0] if predictions else 50000.0)

            # Generate prediction dates
            base_date = datetime.now()
            prediction_dates = [(base_date + timedelta(days=i + 1)).strftime("%Y-%m-%d") for i in range(days_ahead)]

            # Calculate overall confidence
            overall_confidence = np.mean(confidence_scores) if confidence_scores else 0.7

            result = {
                "symbol": symbol.upper(),
                "current_price": float(current_price),
                "predicted_prices": predictions,
                "prediction_dates": prediction_dates,
                "confidence_score": float(overall_confidence),
                "volatility_score": self._calculate_volatility_score(processed_data),
                "model_version": f"crypto_lstm_v1.0.0",
                "created_at": datetime.now(),
                "days_ahead": days_ahead,
                "model_info": self.model.get_model_info() if self.model else {}
            }

            logger.info(f"Generated crypto prediction for {symbol} with confidence {overall_confidence:.2f}")
            return result

        except Exception as e:
            logger.error(f"Error predicting crypto prices for {symbol}: {e}")
            return await self._generate_fallback_prediction(symbol, days_ahead)

    async def train_model(self, symbol: str, force_retrain: bool = False) -> Dict:
        """
        Train LSTM model for cryptocurrency prediction

        Args:
            symbol: Cryptocurrency symbol
            force_retrain: Whether to force retraining even if model exists

        Returns:
            Training result dictionary
        """
        try:
            logger.info(f"Training crypto LSTM model for {symbol}")

            # Get historical data (more data for crypto due to volatility)
            historical_data = await crypto_service.get_historical_data(
                symbol=symbol,
                start_date=datetime.now() - timedelta(days=365),  # 1 year of data
                end_date=datetime.now()
            )

            if not historical_data.get('data') or len(historical_data['data']) < 100:
                logger.error(f"Insufficient historical data for {symbol}")
                return {"success": False, "error": "Insufficient historical data"}

            # Preprocess data
            processed_data, df = self.preprocessor.preprocess_crypto_data(historical_data['data'])

            if len(processed_data) < self.preprocessor.sequence_length + 30:
                logger.error(f"Not enough processed data for {symbol}")
                return {"success": False, "error": "Not enough processed data"}

            # Prepare training sequences
            X, y = self.preprocessor.prepare_sequences(processed_data)

            if len(X) < 20:
                logger.error(f"Not enough training sequences for {symbol}")
                return {"success": False, "error": "Not enough training sequences"}

            # Create and train model
            self.model = CryptoLSTMModel(
                input_size=processed_data.shape[1],
                hidden_size=64,  # Larger hidden size for crypto complexity
                num_layers=2,
                output_size=1
            )

            # Train with crypto-optimized parameters
            training_history = self.model.train(
                X, y,
                epochs=100,  # More epochs for crypto volatility
                learning_rate=0.0005,  # Lower learning rate for stability
                validation_split=0.2
            )

            # Save model
            self.symbol = symbol
            model_path = os.path.join(self.model_dir, f"{symbol.lower()}_model.json")
            self.model.save_model(model_path)
            self.last_training_data_date = datetime.now()

            result = {
                "success": True,
                "symbol": symbol.upper(),
                "training_samples": len(X),
                "final_loss": training_history['train_loss'][-1] if training_history['train_loss'] else 0,
                "epochs": len(training_history['train_loss']),
                "model_path": model_path,
                "trained_at": datetime.now().isoformat()
            }

            logger.info(f"Successfully trained crypto model for {symbol}")
            return result

        except Exception as e:
            logger.error(f"Error training crypto model for {symbol}: {e}")
            return {"success": False, "error": str(e)}

    async def load_model(self, symbol: str) -> bool:
        """
        Load existing model for the given symbol

        Args:
            symbol: Cryptocurrency symbol

        Returns:
            True if model loaded successfully, False otherwise
        """
        try:
            model_path = os.path.join(self.model_dir, f"{symbol.lower()}_model.json")

            if not os.path.exists(model_path):
                return False

            # Create model instance
            self.model = CryptoLSTMModel(input_size=1, hidden_size=64, num_layers=2)

            # Load model data
            if self.model.load_model(model_path):
                self.symbol = symbol
                logger.info(f"Loaded crypto model for {symbol}")
                return True

            return False

        except Exception as e:
            logger.error(f"Error loading crypto model for {symbol}: {e}")
            return False

    def get_model_info(self, symbol: str) -> Dict:
        """
        Get information about the model for a given symbol

        Args:
            symbol: Cryptocurrency symbol

        Returns:
            Model information dictionary
        """
        try:
            model_path = os.path.join(self.model_dir, f"{symbol.lower()}_model.json")

            if self.model and self.symbol == symbol:
                return {
                    "symbol": symbol.upper(),
                    "model_exists": True,
                    "model_info": self.model.get_model_info(),
                    "model_path": model_path
                }
            elif os.path.exists(model_path):
                return {
                    "symbol": symbol.upper(),
                    "model_exists": True,
                    "model_path": model_path,
                    "note": "Model file exists but not loaded"
                }
            else:
                return {
                    "symbol": symbol.upper(),
                    "model_exists": False,
                    "model_path": model_path
                }

        except Exception as e:
            logger.error(f"Error getting crypto model info for {symbol}: {e}")
            return {
                "symbol": symbol.upper(),
                "model_exists": False,
                "error": str(e)
            }

    async def _should_retrain_model(self, symbol: str) -> bool:
        """
        Determine if model should be retrained based on data freshness
        Crypto models need more frequent retraining due to market volatility

        Args:
            symbol: Cryptocurrency symbol

        Returns:
            True if model should be retrained
        """
        if not self.model or not self.model.last_trained:
            return True

        # Retrain crypto models more frequently (every 3 days vs 7 for stocks)
        days_since_training = (datetime.now() - self.model.last_trained).days
        return days_since_training > 3

    def _calculate_crypto_confidence(self, data: np.ndarray, prediction: np.ndarray, day_ahead: int) -> float:
        """
        Calculate confidence score for crypto prediction based on volatility and trends

        Args:
            data: Historical processed data
            prediction: Current prediction
            day_ahead: Days ahead being predicted

        Returns:
            Confidence score between 0 and 1
        """
        try:
            # Base confidence starts lower for crypto due to volatility
            base_confidence = 0.6

            # Reduce confidence for longer-term predictions more aggressively
            time_decay = max(0.1, 1.0 - (day_ahead * 0.05))

            # Calculate recent volatility (crypto-specific)
            recent_prices = data[-30:, 0] if len(data) >= 30 else data[:, 0]
            volatility = np.std(recent_prices) / np.mean(recent_prices) if len(recent_prices) > 1 else 0.1

            # Higher volatility = lower confidence (more aggressive for crypto)
            volatility_factor = max(0.2, 1.0 - (volatility * 3))

            # Combine factors
            confidence = base_confidence * time_decay * volatility_factor

            return max(0.1, min(0.95, confidence))

        except Exception:
            return 0.5

    def _calculate_volatility_score(self, data: np.ndarray) -> float:
        """
        Calculate volatility score for the cryptocurrency

        Args:
            data: Historical processed data

        Returns:
            Volatility score between 0 and 1
        """
        try:
            recent_prices = data[-30:, 0] if len(data) >= 30 else data[:, 0]
            if len(recent_prices) > 1:
                volatility = np.std(recent_prices) / np.mean(recent_prices)
                # Normalize volatility score (crypto typically has higher volatility)
                return min(1.0, volatility * 5)
            return 0.5
        except Exception:
            return 0.5

    async def _generate_fallback_prediction(self, symbol: str, days_ahead: int) -> Dict:
        """
        Generate fallback prediction when LSTM model fails

        Args:
            symbol: Cryptocurrency symbol
            days_ahead: Number of days to predict

        Returns:
            Fallback prediction dictionary
        """
        try:
            # Get current data
            current_data = await crypto_service.get_crypto_data(symbol)
            current_price = current_data.get('current_price', 50000.0)

            # Generate crypto-style predictions with higher volatility
            predictions = []
            base_date = datetime.now()
            prediction_dates = []

            for i in range(days_ahead):
                # Crypto fallback with higher volatility
                daily_change = np.random.normal(0, 0.03)  # 3% daily volatility
                predicted_price = current_price * (1 + daily_change)
                predictions.append(max(0.01, predicted_price))

                pred_date = base_date + timedelta(days=i + 1)
                prediction_dates.append(pred_date.strftime("%Y-%m-%d"))

                current_price = predicted_price

            return {
                "symbol": symbol.upper(),
                "current_price": float(current_data.get('current_price', 50000.0)),
                "predicted_prices": predictions,
                "prediction_dates": prediction_dates,
                "confidence_score": 0.4,  # Lower confidence for fallback
                "volatility_score": 0.8,  # High volatility for crypto
                "model_version": "crypto_fallback_v1.0.0",
                "created_at": datetime.now(),
                "days_ahead": days_ahead,
                "note": "Fallback prediction using volatility-based model"
            }

        except Exception as e:
            logger.error(f"Error generating crypto fallback prediction: {e}")
            # Ultimate fallback
            base_price = 50000.0
            predictions = [base_price * (1 + np.random.normal(0, 0.02)) for _ in range(days_ahead)]
            prediction_dates = [(datetime.now() + timedelta(days=i + 1)).strftime("%Y-%m-%d") for i in range(days_ahead)]

            return {
                "symbol": symbol.upper(),
                "current_price": base_price,
                "predicted_prices": predictions,
                "prediction_dates": prediction_dates,
                "confidence_score": 0.3,
                "volatility_score": 0.9,
                "model_version": "crypto_mock_v1.0.0",
                "created_at": datetime.now(),
                "days_ahead": days_ahead,
                "note": "Mock crypto prediction data"
            }


# Create global instance
crypto_predictor = CryptoPredictor()
    
    async def predict_prices(self, symbol: str, days_ahead: int = 30) -> Dict:
        """
        Predict cryptocurrency prices for the specified number of days
        
        Args:
            symbol: Cryptocurrency symbol (e.g., 'BTC', 'ETH')
            days_ahead: Number of days to predict ahead
            
        Returns:
            Dictionary containing predictions and metadata
        """
        try:
            logger.info(f"Generating crypto prediction for {symbol}, {days_ahead} days ahead")
            
            # Load or train model
            if self.model is None or self.symbol != symbol:
                model_loaded = await self.load_model(symbol)
                if not model_loaded:
                    logger.info(f"No existing model found for {symbol}, training new model")
                    training_result = await self.train_model(symbol)
                    if not training_result.get('success', False):
                        logger.warning(f"Training failed for {symbol}, using fallback prediction")
                        return await self._generate_fallback_prediction(symbol, days_ahead)
            
            # Check if model needs retraining (crypto markets change quickly)
            if await self._should_retrain_model(symbol):
                logger.info(f"Model for {symbol} needs retraining")
                await self.train_model(symbol, force_retrain=True)
            
            # Get recent data for prediction
            historical_data = await crypto_service.get_historical_data(
                symbol=symbol,
                start_date=datetime.now() - timedelta(days=90),
                end_date=datetime.now()
            )
            
            if not historical_data.get('data'):
                logger.warning(f"No historical data available for {symbol}")
                return await self._generate_fallback_prediction(symbol, days_ahead)
            
            # Preprocess data
            processed_data, df = self.preprocessor.preprocess_crypto_data(historical_data['data'])
            
            if len(processed_data) < self.preprocessor.sequence_length:
                logger.warning(f"Insufficient data for {symbol} prediction")
                return await self._generate_fallback_prediction(symbol, days_ahead)
            
            # Generate predictions
            predictions = []
            confidence_scores = []
            
            # Use the last sequence for prediction
            current_sequence = processed_data[-self.preprocessor.sequence_length:].reshape(1, -1, processed_data.shape[1])
            
            for day in range(days_ahead):
                # Predict next value
                pred = self.model.predict(current_sequence)
                
                # Denormalize prediction (assuming first column is close price)
                denormalized_pred = self.preprocessor.denormalize_data(pred, 'crypto_features')[0]
                predictions.append(float(denormalized_pred))
                
                # Calculate confidence based on crypto volatility
                confidence = self._calculate_crypto_confidence(processed_data, pred, day)
                confidence_scores.append(confidence)
                
                # Update sequence for next prediction (simplified approach)
                # In practice, you'd want to update with the predicted values
                current_sequence = np.roll(current_sequence, -1, axis=1)
                current_sequence[0, -1, 0] = pred[0]  # Update close price
            
            # Get current price
            current_data = await crypto_service.get_crypto_data(symbol)
            current_price = current_data.get('current_price', predictions[0] if predictions else 50000.0)
            
            # Generate prediction dates
            base_date = datetime.now()
            prediction_dates = [(base_date + timedelta(days=i + 1)).strftime("%Y-%m-%d") for i in range(days_ahead)]
            
            # Calculate overall confidence
            overall_confidence = np.mean(confidence_scores) if confidence_scores else 0.7
            
            result = {
                "symbol": symbol.upper(),
                "current_price": float(current_price),
                "predicted_prices": predictions,
                "prediction_dates": prediction_dates,
                "confidence_score": float(overall_confidence),
                "volatility_score": self._calculate_volatility_score(processed_data),
                "model_version": f"crypto_lstm_v1.0.0",
                "created_at": datetime.now(),
                "days_ahead": days_ahead,
                "model_info": self.model.get_model_info() if self.model else {}
            }
            
            logger.info(f"Generated crypto prediction for {symbol} with confidence {overall_confidence:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"Error predicting crypto prices for {symbol}: {e}")
            return await self._generate_fallback_prediction(symbol, days_ahead)
    
    async def train_model(self, symbol: str, force_retrain: bool = False) -> Dict:
        """
        Train LSTM model for cryptocurrency prediction
        
        Args:
            symbol: Cryptocurrency symbol
            force_retrain: Whether to force retraining even if model exists
            
        Returns:
            Training result dictionary
        """
        try:
            logger.info(f"Training crypto LSTM model for {symbol}")
            
            # Get historical data (more data for crypto due to volatility)
            historical_data = await crypto_service.get_historical_data(
                symbol=symbol,
                start_date=datetime.now() - timedelta(days=365),  # 1 year of data
                end_date=datetime.now()
            )
            
            if not historical_data.get('data') or len(historical_data['data']) < 100:
                logger.error(f"Insufficient historical data for {symbol}")
                return {"success": False, "error": "Insufficient historical data"}
            
            # Preprocess data
            processed_data, df = self.preprocessor.preprocess_crypto_data(historical_data['data'])
            
            if len(processed_data) < self.preprocessor.sequence_length + 30:
                logger.error(f"Not enough processed data for {symbol}")
                return {"success": False, "error": "Not enough processed data"}
            
            # Prepare training sequences
            X, y = self.preprocessor.prepare_sequences(processed_data)
            
            if len(X) < 20:
                logger.error(f"Not enough training sequences for {symbol}")
                return {"success": False, "error": "Not enough training sequences"}
            
            # Create and train model
            self.model = CryptoLSTMModel(
                input_size=processed_data.shape[1],
                hidden_size=64,  # Larger hidden size for crypto complexity
                num_layers=2,
                output_size=1
            )
            
            # Train with crypto-optimized parameters
            training_history = self.model.train(
                X, y,
                epochs=100,  # More epochs for crypto volatility
                learning_rate=0.0005,  # Lower learning rate for stability
                validation_split=0.2
            )
            
            # Save model
            self.symbol = symbol
            model_path = os.path.join(self.model_dir, f"{symbol.lower()}_model.json")
            self.model.save_model(model_path)
            self.last_training_data_date = datetime.now()
            
            result = {
                "success": True,
                "symbol": symbol.upper(),
                "training_samples": len(X),
                "final_loss": training_history['train_loss'][-1] if training_history['train_loss'] else 0,
                "epochs": len(training_history['train_loss']),
                "model_path": model_path,
                "trained_at": datetime.now().isoformat()
            }
            
            logger.info(f"Successfully trained crypto model for {symbol}")
            return result
            
        except Exception as e:
            logger.error(f"Error training crypto model for {symbol}: {e}")
            return {"success": False, "error": str(e)}
    
    async def load_model(self, symbol: str) -> bool:
        """
        Load existing model for the given symbol
        
        Args:
            symbol: Cryptocurrency symbol
            
        Returns:
            True if model loaded successfully, False otherwise
        """
        try:
            model_path = os.path.join(self.model_dir, f"{symbol.lower()}_model.json")
            
            if not os.path.exists(model_path):
                return False
            
            # Create model instance
            self.model = CryptoLSTMModel(input_size=1, hidden_size=64, num_layers=2)
            
            # Load model data
            if self.model.load_model(model_path):
                self.symbol = symbol
                logger.info(f"Loaded crypto model for {symbol}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error loading crypto model for {symbol}: {e}")
            return False
    
    def get_model_info(self, symbol: str) -> Dict:
        """
        Get information about the model for a given symbol
        
        Args:
            symbol: Cryptocurrency symbol
            
        Returns:
            Model information dictionary
        """
        try:
            model_path = os.path.join(self.model_dir, f"{symbol.lower()}_model.json")
            
            if self.model and self.symbol == symbol:
                return {
                    "symbol": symbol.upper(),
                    "model_exists": True,
                    "model_info": self.model.get_model_info(),
                    "model_path": model_path
                }
            elif os.path.exists(model_path):
                return {
                    "symbol": symbol.upper(),
                    "model_exists": True,
                    "model_path": model_path,
                    "note": "Model file exists but not loaded"
                }
            else:
                return {
                    "symbol": symbol.upper(),
                    "model_exists": False,
                    "model_path": model_path
                }
                
        except Exception as e:
            logger.error(f"Error getting crypto model info for {symbol}: {e}")
            return {
                "symbol": symbol.upper(),
                "model_exists": False,
                "error": str(e)
            }
    
    async def _should_retrain_model(self, symbol: str) -> bool:
        """
        Determine if model should be retrained based on data freshness
        Crypto models need more frequent retraining due to market volatility
        
        Args:
            symbol: Cryptocurrency symbol
            
        Returns:
            True if model should be retrained
        """
        if not self.model or not self.model.last_trained:
            return True
        
        # Retrain crypto models more frequently (every 3 days vs 7 for stocks)
        days_since_training = (datetime.now() - self.model.last_trained).days
        return days_since_training > 3
    
    def _calculate_crypto_confidence(self, data: np.ndarray, prediction: np.ndarray, day_ahead: int) -> float:
        """
        Calculate confidence score for crypto prediction based on volatility and trends
        
        Args:
            data: Historical processed data
            prediction: Current prediction
            day_ahead: Days ahead being predicted
            
        Returns:
            Confidence score between 0 and 1
        """
        try:
            # Base confidence starts lower for crypto due to volatility
            base_confidence = 0.6
            
            # Reduce confidence for longer-term predictions more aggressively
            time_decay = max(0.1, 1.0 - (day_ahead * 0.05))
            
            # Calculate recent volatility (crypto-specific)
            recent_prices = data[-30:, 0] if len(data) >= 30 else data[:, 0]
            volatility = np.std(recent_prices) / np.mean(recent_prices) if len(recent_prices) > 1 else 0.1
            
            # Higher volatility = lower confidence (more aggressive for crypto)
            volatility_factor = max(0.2, 1.0 - (volatility * 3))
            
            # Combine factors
            confidence = base_confidence * time_decay * volatility_factor
            
            return max(0.1, min(0.95, confidence))
            
        except Exception:
            return 0.5
    
    def _calculate_volatility_score(self, data: np.ndarray) -> float:
        """
        Calculate volatility score for the cryptocurrency
        
        Args:
            data: Historical processed data
            
        Returns:
            Volatility score between 0 and 1
        """
        try:
            recent_prices = data[-30:, 0] if len(data) >= 30 else data[:, 0]
            if len(recent_prices) > 1:
                volatility = np.std(recent_prices) / np.mean(recent_prices)
                # Normalize volatility score (crypto typically has higher volatility)
                return min(1.0, volatility * 5)
            return 0.5
        except Exception:
            return 0.5
    
    async def _generate_fallback_prediction(self, symbol: str, days_ahead: int) -> Dict:
        """
        Generate fallback prediction when LSTM model fails
        
        Args:
            symbol: Cryptocurrency symbol
            days_ahead: Number of days to predict
            
        Returns:
            Fallback prediction dictionary
        """
        try:
            # Get current data
            current_data = await crypto_service.get_crypto_data(symbol)
            current_price = current_data.get('current_price', 50000.0)
            
            # Generate crypto-style predictions with higher volatility
            predictions = []
            base_date = datetime.now()
            prediction_dates = []
            
            for i in range(days_ahead):
                # Crypto fallback with higher volatility
                daily_change = np.random.normal(0, 0.03)  # 3% daily volatility
                predicted_price = current_price * (1 + daily_change)
                predictions.append(max(0.01, predicted_price))
                
                pred_date = base_date + timedelta(days=i + 1)
                prediction_dates.append(pred_date.strftime("%Y-%m-%d"))
                
                current_price = predicted_price
            
            return {
                "symbol": symbol.upper(),
                "current_price": float(current_data.get('current_price', 50000.0)),
                "predicted_prices": predictions,
                "prediction_dates": prediction_dates,
                "confidence_score": 0.4,  # Lower confidence for fallback
                "volatility_score": 0.8,  # High volatility for crypto
                "model_version": "crypto_fallback_v1.0.0",
                "created_at": datetime.now(),
                "days_ahead": days_ahead,
                "note": "Fallback prediction using volatility-based model"
            }
            
        except Exception as e:
            logger.error(f"Error generating crypto fallback prediction: {e}")
            # Ultimate fallback
            base_price = 50000.0
            predictions = [base_price * (1 + np.random.normal(0, 0.02)) for _ in range(days_ahead)]
            prediction_dates = [(datetime.now() + timedelta(days=i + 1)).strftime("%Y-%m-%d") for i in range(days_ahead)]
            
            return {
                "symbol": symbol.upper(),
                "current_price": base_price,
                "predicted_prices": predictions,
                "prediction_dates": prediction_dates,
                "confidence_score": 0.3,
                "volatility_score": 0.9,
                "model_version": "crypto_mock_v1.0.0",
                "created_at": datetime.now(),
                "days_ahead": days_ahead,
                "note": "Mock crypto prediction data"
            }


# Create global instance
crypto_predictor = CryptoPredictor()
