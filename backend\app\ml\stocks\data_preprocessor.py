"""
Stock data preprocessing module for LSTM model training
"""

import numpy as np
import pandas as pd
from typing import Tuple, List, Optional, Dict
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


class StockDataPreprocessor:
    """
    Preprocesses stock data for LSTM model training and prediction
    """
    
    def __init__(self, sequence_length: int = 60, prediction_days: int = 30):
        """
        Initialize the preprocessor
        
        Args:
            sequence_length: Number of days to look back for prediction
            prediction_days: Number of days to predict ahead
        """
        self.sequence_length = sequence_length
        self.prediction_days = prediction_days
        self.scaler_params = {}
        
    def normalize_data(self, data: np.ndarray, feature_name: str = "price") -> np.ndarray:
        """
        Normalize data using min-max scaling
        
        Args:
            data: Input data array
            feature_name: Name of the feature for storing scaler parameters
            
        Returns:
            Normalized data array
        """
        min_val = np.min(data)
        max_val = np.max(data)
        
        # Store scaler parameters for inverse transformation
        self.scaler_params[feature_name] = {
            'min': min_val,
            'max': max_val
        }
        
        # Avoid division by zero
        if max_val - min_val == 0:
            return np.zeros_like(data)
            
        normalized = (data - min_val) / (max_val - min_val)
        return normalized
    
    def denormalize_data(self, normalized_data: np.ndarray, feature_name: str = "price") -> np.ndarray:
        """
        Denormalize data using stored scaler parameters
        
        Args:
            normalized_data: Normalized data array
            feature_name: Name of the feature to get scaler parameters
            
        Returns:
            Denormalized data array
        """
        if feature_name not in self.scaler_params:
            logger.warning(f"No scaler parameters found for {feature_name}")
            return normalized_data
            
        params = self.scaler_params[feature_name]
        denormalized = normalized_data * (params['max'] - params['min']) + params['min']
        return denormalized
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate technical indicators for enhanced prediction
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with additional technical indicators
        """
        df = df.copy()
        
        # Simple Moving Averages
        df['SMA_5'] = df['close'].rolling(window=5).mean()
        df['SMA_10'] = df['close'].rolling(window=10).mean()
        df['SMA_20'] = df['close'].rolling(window=20).mean()
        
        # Exponential Moving Average
        df['EMA_12'] = df['close'].ewm(span=12).mean()
        df['EMA_26'] = df['close'].ewm(span=26).mean()
        
        # MACD
        df['MACD'] = df['EMA_12'] - df['EMA_26']
        df['MACD_signal'] = df['MACD'].ewm(span=9).mean()
        
        # RSI (Relative Strength Index)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        
        # Bollinger Bands
        df['BB_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['BB_upper'] = df['BB_middle'] + (bb_std * 2)
        df['BB_lower'] = df['BB_middle'] - (bb_std * 2)
        
        # Volume indicators
        df['volume_sma'] = df['volume'].rolling(window=10).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        # Price change indicators
        df['price_change'] = df['close'].pct_change()
        df['high_low_ratio'] = df['high'] / df['low']
        
        return df
    
    def prepare_sequences(self, data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare sequences for LSTM training
        
        Args:
            data: Preprocessed data array
            
        Returns:
            Tuple of (X, y) where X is input sequences and y is target values
        """
        X, y = [], []
        
        for i in range(self.sequence_length, len(data)):
            # Input sequence (past sequence_length days)
            X.append(data[i-self.sequence_length:i])
            # Target (next day's price)
            y.append(data[i])
            
        return np.array(X), np.array(y)
    
    def prepare_prediction_sequences(self, data: np.ndarray) -> np.ndarray:
        """
        Prepare sequences for prediction (no target values)
        
        Args:
            data: Preprocessed data array
            
        Returns:
            Input sequences for prediction
        """
        if len(data) < self.sequence_length:
            # Pad with zeros if not enough data
            padding = np.zeros((self.sequence_length - len(data), data.shape[1] if len(data.shape) > 1 else 1))
            if len(data.shape) == 1:
                data = data.reshape(-1, 1)
                padding = padding.reshape(-1, 1)
            data = np.vstack([padding, data])
        
        # Take the last sequence_length days
        sequence = data[-self.sequence_length:]
        return sequence.reshape(1, self.sequence_length, -1)
    
    def preprocess_stock_data(self, historical_data: List[Dict]) -> Tuple[np.ndarray, pd.DataFrame]:
        """
        Complete preprocessing pipeline for stock data
        
        Args:
            historical_data: List of dictionaries with OHLCV data
            
        Returns:
            Tuple of (processed_data_array, original_dataframe)
        """
        # Convert to DataFrame
        df = pd.DataFrame(historical_data)
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date').reset_index(drop=True)
        
        # Calculate technical indicators
        df = self.calculate_technical_indicators(df)
        
        # Select features for model
        feature_columns = [
            'close', 'volume', 'high', 'low', 'open',
            'SMA_5', 'SMA_10', 'SMA_20', 'EMA_12', 'EMA_26',
            'MACD', 'MACD_signal', 'RSI', 'BB_upper', 'BB_lower',
            'volume_ratio', 'price_change', 'high_low_ratio'
        ]
        
        # Remove rows with NaN values (from technical indicators)
        df = df.dropna()
        
        if len(df) < self.sequence_length:
            raise ValueError(f"Not enough data points. Need at least {self.sequence_length}, got {len(df)}")
        
        # Extract features
        features = df[feature_columns].values
        
        # Normalize each feature
        normalized_features = np.zeros_like(features)
        for i, col in enumerate(feature_columns):
            normalized_features[:, i] = self.normalize_data(features[:, i], col)
        
        return normalized_features, df
    
    def get_feature_importance(self) -> Dict[str, float]:
        """
        Get feature importance weights (simplified version)
        In a real implementation, this would come from model training
        
        Returns:
            Dictionary of feature names and their importance scores
        """
        return {
            'close': 0.25,
            'volume': 0.15,
            'SMA_20': 0.12,
            'RSI': 0.10,
            'MACD': 0.08,
            'BB_upper': 0.06,
            'BB_lower': 0.06,
            'EMA_12': 0.05,
            'EMA_26': 0.05,
            'volume_ratio': 0.04,
            'price_change': 0.04
        }
