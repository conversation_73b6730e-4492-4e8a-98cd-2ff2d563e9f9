"""
Tests for rate limiting functionality
"""

import pytest
import time
from fastapi.testclient import TestClient

from app.core.rate_limiter import RateLimiter
from app.models.user import User, SubscriptionTier


class TestRateLimiter:
    """Test rate limiter core functionality."""
    
    def test_rate_limiter_initialization(self):
        """Test rate limiter initializes correctly."""
        rate_limiter = RateLimiter()
        assert rate_limiter.requests == {}
    
    def test_rate_limiter_allows_within_limit(self):
        """Test rate limiter allows requests within limit."""
        rate_limiter = RateLimiter()
        identifier = "test_user"
        limit = 5
        
        # Make requests within limit
        for i in range(limit):
            result = rate_limiter.is_allowed(identifier, limit, window_seconds=3600)
            assert result is True
    
    def test_rate_limiter_blocks_over_limit(self):
        """Test rate limiter blocks requests over limit."""
        rate_limiter = RateLimiter()
        identifier = "test_user"
        limit = 3
        
        # Make requests up to limit
        for i in range(limit):
            result = rate_limiter.is_allowed(identifier, limit, window_seconds=3600)
            assert result is True
        
        # Next request should be blocked
        result = rate_limiter.is_allowed(identifier, limit, window_seconds=3600)
        assert result is False
    
    def test_rate_limiter_sliding_window(self):
        """Test rate limiter sliding window behavior."""
        rate_limiter = RateLimiter()
        identifier = "test_user"
        limit = 2
        window_seconds = 1  # 1 second window
        
        # Make requests up to limit
        assert rate_limiter.is_allowed(identifier, limit, window_seconds) is True
        assert rate_limiter.is_allowed(identifier, limit, window_seconds) is True
        
        # Should be blocked
        assert rate_limiter.is_allowed(identifier, limit, window_seconds) is False
        
        # Wait for window to slide
        time.sleep(1.1)
        
        # Should be allowed again
        assert rate_limiter.is_allowed(identifier, limit, window_seconds) is True
    
    def test_rate_limiter_different_identifiers(self):
        """Test rate limiter handles different identifiers separately."""
        rate_limiter = RateLimiter()
        limit = 2
        
        # User 1 makes requests
        assert rate_limiter.is_allowed("user1", limit, window_seconds=3600) is True
        assert rate_limiter.is_allowed("user1", limit, window_seconds=3600) is True
        assert rate_limiter.is_allowed("user1", limit, window_seconds=3600) is False
        
        # User 2 should still be allowed
        assert rate_limiter.is_allowed("user2", limit, window_seconds=3600) is True
        assert rate_limiter.is_allowed("user2", limit, window_seconds=3600) is True
    
    def test_rate_limiter_cleanup(self):
        """Test rate limiter cleanup functionality."""
        rate_limiter = RateLimiter()
        identifier = "test_user"
        
        # Add some requests
        rate_limiter.is_allowed(identifier, 5, window_seconds=1)
        assert identifier in rate_limiter.requests
        
        # Wait for cleanup
        time.sleep(1.1)
        
        # Make another request (should trigger cleanup)
        rate_limiter.is_allowed(identifier, 5, window_seconds=1)
        
        # Old requests should be cleaned up
        request_queue = rate_limiter.requests[identifier]
        assert len(request_queue) == 1  # Only the new request
    
    def test_get_rate_limit_info(self):
        """Test getting rate limit information."""
        rate_limiter = RateLimiter()
        identifier = "test_user"
        limit = 5
        
        # Make some requests
        for i in range(3):
            rate_limiter.is_allowed(identifier, limit, window_seconds=3600)
        
        info = rate_limiter.get_rate_limit_info(identifier, limit, window_seconds=3600)
        
        assert info["limit"] == limit
        assert info["remaining"] == limit - 3
        assert info["reset_time"] is not None
        assert info["used"] == 3
        assert info["window_seconds"] == 3600


class TestRateLimitingEndpoints:
    """Test rate limiting on API endpoints."""
    
    def test_prediction_endpoint_rate_limiting(self, client: TestClient, auth_headers: dict):
        """Test rate limiting on prediction endpoints."""
        prediction_data = {
            "symbol": "AAPL",
            "days_ahead": 5,
            "include_volume_analysis": True
        }
        
        # Make requests up to the limit
        # Note: This test depends on the actual rate limits configured
        responses = []
        for i in range(10):  # Try to make more requests than typical limit
            response = client.post(
                "/api/v1/stocks/AAPL/predict",
                json=prediction_data,
                headers=auth_headers
            )
            responses.append(response)
            
            # Check for rate limit headers
            if response.status_code == 200:
                assert "X-RateLimit-Limit" in response.headers
                assert "X-RateLimit-Remaining" in response.headers
                assert "X-RateLimit-Reset" in response.headers
        
        # At least one request should be rate limited if we exceed limits
        rate_limited_responses = [r for r in responses if r.status_code == 429]
        # Note: This might not trigger in test environment depending on limits
    
    def test_rate_limit_headers_present(self, client: TestClient, auth_headers: dict):
        """Test that rate limit headers are present in responses."""
        response = client.get("/api/v1/subscriptions/usage", headers=auth_headers)
        
        # Rate limit headers should be present
        expected_headers = ["X-RateLimit-Limit", "X-RateLimit-Remaining", "X-RateLimit-Reset"]
        for header in expected_headers:
            assert header in response.headers
    
    def test_rate_limit_different_tiers(self, client: TestClient, auth_headers: dict, premium_auth_headers: dict):
        """Test that different subscription tiers have different rate limits."""
        # Make request with free tier user
        response_free = client.get("/api/v1/subscriptions/usage", headers=auth_headers)
        free_limit = int(response_free.headers.get("X-RateLimit-Limit", "0"))
        
        # Make request with premium tier user
        response_premium = client.get("/api/v1/subscriptions/usage", headers=premium_auth_headers)
        premium_limit = int(response_premium.headers.get("X-RateLimit-Limit", "0"))
        
        # Premium should have higher limits
        assert premium_limit > free_limit
    
    def test_rate_limit_429_response(self, client: TestClient):
        """Test 429 response when rate limit is exceeded."""
        # This test would require actually exceeding rate limits
        # For now, we'll test the structure of what a 429 response should look like
        
        # In a real scenario, you'd make many requests to trigger rate limiting
        # response = client.get("/api/v1/some-endpoint")
        # if response.status_code == 429:
        #     assert "Rate limit exceeded" in response.json()["detail"]
        #     assert "Retry-After" in response.headers
        
        # For now, we'll just verify the concept
        assert True  # Placeholder for actual rate limit testing


class TestRateLimitingIntegration:
    """Test rate limiting integration with subscription system."""
    
    def test_rate_limit_respects_subscription_tier(self, client: TestClient, db_session):
        """Test that rate limits respect subscription tiers."""
        from app.core.auth import create_access_token
        from app.core.auth import get_password_hash
        
        # Create users with different tiers
        free_user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            subscription_tier=SubscriptionTier.FREE,
            is_active=True
        )
        
        premium_user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            subscription_tier=SubscriptionTier.PREMIUM,
            is_active=True
        )
        
        db_session.add(free_user)
        db_session.add(premium_user)
        db_session.commit()
        
        # Create auth headers
        free_token = create_access_token(data={"sub": free_user.email})
        premium_token = create_access_token(data={"sub": premium_user.email})
        
        free_headers = {"Authorization": f"Bearer {free_token}"}
        premium_headers = {"Authorization": f"Bearer {premium_token}"}
        
        # Test rate limits
        free_response = client.get("/api/v1/subscriptions/usage", headers=free_headers)
        premium_response = client.get("/api/v1/subscriptions/usage", headers=premium_headers)
        
        if free_response.status_code == 200 and premium_response.status_code == 200:
            free_limit = int(free_response.headers.get("X-RateLimit-Limit", "0"))
            premium_limit = int(premium_response.headers.get("X-RateLimit-Limit", "0"))
            
            assert premium_limit > free_limit
    
    def test_rate_limit_enforcement_on_predictions(self, client: TestClient, auth_headers: dict):
        """Test rate limit enforcement specifically on prediction endpoints."""
        prediction_data = {
            "symbol": "AAPL",
            "days_ahead": 5
        }
        
        # Make a prediction request
        response = client.post(
            "/api/v1/stocks/AAPL/predict",
            json=prediction_data,
            headers=auth_headers
        )
        
        # Should have rate limit headers regardless of success/failure
        if response.status_code in [200, 400, 401]:  # Valid response codes
            assert "X-RateLimit-Limit" in response.headers
    
    def test_rate_limit_bypass_for_admin(self, client: TestClient, admin_auth_headers: dict):
        """Test that admin users might have different rate limits."""
        response = client.get("/api/v1/admin/stats", headers=admin_auth_headers)
        
        if response.status_code == 200:
            # Admin endpoints might have different or no rate limits
            # This depends on implementation
            assert "X-RateLimit-Limit" in response.headers or response.status_code == 200


class TestRateLimitingEdgeCases:
    """Test edge cases in rate limiting."""
    
    def test_rate_limiter_with_zero_limit(self):
        """Test rate limiter behavior with zero limit."""
        rate_limiter = RateLimiter()
        identifier = "test_user"
        
        result = rate_limiter.is_allowed(identifier, 0, window_seconds=3600)
        assert result is False
    
    def test_rate_limiter_with_negative_limit(self):
        """Test rate limiter behavior with negative limit."""
        rate_limiter = RateLimiter()
        identifier = "test_user"
        
        result = rate_limiter.is_allowed(identifier, -1, window_seconds=3600)
        assert result is False
    
    def test_rate_limiter_with_very_short_window(self):
        """Test rate limiter with very short time window."""
        rate_limiter = RateLimiter()
        identifier = "test_user"
        
        # Very short window (0.1 seconds)
        assert rate_limiter.is_allowed(identifier, 1, window_seconds=0.1) is True
        assert rate_limiter.is_allowed(identifier, 1, window_seconds=0.1) is False
        
        # Wait for window to pass
        time.sleep(0.2)
        assert rate_limiter.is_allowed(identifier, 1, window_seconds=0.1) is True
    
    def test_rate_limiter_memory_cleanup(self):
        """Test that rate limiter cleans up memory properly."""
        rate_limiter = RateLimiter()
        
        # Create many identifiers
        for i in range(100):
            identifier = f"user_{i}"
            rate_limiter.is_allowed(identifier, 5, window_seconds=1)
        
        # Wait for cleanup
        time.sleep(1.1)
        
        # Make new requests to trigger cleanup
        for i in range(10):
            identifier = f"user_{i}"
            rate_limiter.is_allowed(identifier, 5, window_seconds=1)
        
        # Old entries should be cleaned up
        # This is hard to test directly, but we can verify the system still works
        assert rate_limiter.is_allowed("new_user", 5, window_seconds=1) is True


class TestRateLimitingPerformance:
    """Test rate limiting performance characteristics."""
    
    def test_rate_limiter_performance(self, performance_timer):
        """Test rate limiter performance with many requests."""
        rate_limiter = RateLimiter()
        identifier = "performance_test"
        
        performance_timer.start()
        
        # Make many requests
        for i in range(1000):
            rate_limiter.is_allowed(identifier, 1000, window_seconds=3600)
        
        performance_timer.stop()
        
        # Should complete quickly (less than 1 second for 1000 requests)
        assert performance_timer.elapsed < 1.0
    
    def test_rate_limiter_memory_usage(self):
        """Test rate limiter memory usage doesn't grow unbounded."""
        rate_limiter = RateLimiter()
        
        # Make requests with many different identifiers
        for i in range(1000):
            identifier = f"user_{i}"
            rate_limiter.is_allowed(identifier, 10, window_seconds=1)
        
        initial_size = len(rate_limiter.requests)
        
        # Wait for cleanup
        time.sleep(1.1)
        
        # Make more requests
        for i in range(100):
            identifier = f"new_user_{i}"
            rate_limiter.is_allowed(identifier, 10, window_seconds=1)
        
        # Memory should not grow unbounded
        final_size = len(rate_limiter.requests)
        assert final_size <= initial_size + 100  # Only new users should remain
