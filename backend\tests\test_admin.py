"""
Tests for admin functionality
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session

from app.models.user import User, SubscriptionTier


class TestAdminAccess:
    """Test admin access control."""
    
    def test_admin_access_with_admin_user(self, client: TestClient, admin_auth_headers: dict):
        """Test admin access with admin user."""
        response = client.get("/api/v1/admin/stats", headers=admin_auth_headers)
        
        # Should allow access for admin user
        assert response.status_code == 200
    
    def test_admin_access_denied_for_regular_user(self, client: TestClient, auth_headers: dict):
        """Test admin access denied for regular users."""
        response = client.get("/api/v1/admin/stats", headers=auth_headers)
        
        # Should deny access for regular user
        assert response.status_code == 403
        assert "Admin access required" in response.json()["detail"]
    
    def test_admin_access_requires_authentication(self, client: TestClient):
        """Test admin endpoints require authentication."""
        response = client.get("/api/v1/admin/stats")
        
        # Should require authentication
        assert response.status_code == 401


class TestAdminStats:
    """Test admin statistics endpoints."""
    
    def test_get_admin_stats(self, client: TestClient, admin_auth_headers: dict):
        """Test getting admin statistics."""
        response = client.get("/api/v1/admin/stats", headers=admin_auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        required_fields = [
            "total_users", "active_users", "total_predictions",
            "subscription_breakdown", "api_usage_stats"
        ]
        
        for field in required_fields:
            assert field in data
        
        # Check data types
        assert isinstance(data["total_users"], int)
        assert isinstance(data["active_users"], int)
        assert isinstance(data["total_predictions"], int)
        assert isinstance(data["subscription_breakdown"], dict)
    
    def test_subscription_breakdown_structure(self, client: TestClient, admin_auth_headers: dict):
        """Test subscription breakdown structure."""
        response = client.get("/api/v1/admin/stats", headers=admin_auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        breakdown = data["subscription_breakdown"]
        
        # Should have all subscription tiers
        expected_tiers = ["free", "basic", "premium", "enterprise"]
        for tier in expected_tiers:
            assert tier in breakdown
            assert isinstance(breakdown[tier], int)
    
    def test_api_usage_stats_structure(self, client: TestClient, admin_auth_headers: dict):
        """Test API usage statistics structure."""
        response = client.get("/api/v1/admin/stats", headers=admin_auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        usage_stats = data["api_usage_stats"]
        
        expected_fields = ["total_api_calls", "predictions_today", "average_daily_usage"]
        for field in expected_fields:
            assert field in usage_stats


class TestUserManagement:
    """Test admin user management functionality."""
    
    def test_get_all_users(self, client: TestClient, admin_auth_headers: dict):
        """Test getting all users."""
        response = client.get("/api/v1/admin/users", headers=admin_auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "users" in data
        assert isinstance(data["users"], list)
        
        # Check user structure
        if len(data["users"]) > 0:
            user = data["users"][0]
            expected_fields = ["id", "email", "full_name", "subscription_tier", "is_active", "created_at"]
            for field in expected_fields:
                assert field in user
    
    def test_get_user_by_id(self, client: TestClient, admin_auth_headers: dict, test_user: User):
        """Test getting specific user by ID."""
        response = client.get(f"/api/v1/admin/users/{test_user.id}", headers=admin_auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == test_user.id
        assert data["email"] == test_user.email
        assert data["subscription_tier"] == test_user.subscription_tier.value
    
    def test_get_nonexistent_user(self, client: TestClient, admin_auth_headers: dict):
        """Test getting nonexistent user."""
        response = client.get("/api/v1/admin/users/99999", headers=admin_auth_headers)
        assert response.status_code == 404
        assert "User not found" in response.json()["detail"]
    
    def test_update_user_subscription(self, client: TestClient, admin_auth_headers: dict, test_user: User):
        """Test updating user subscription."""
        update_data = {
            "subscription_tier": "premium"
        }
        
        response = client.put(
            f"/api/v1/admin/users/{test_user.id}",
            json=update_data,
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["subscription_tier"] == "premium"
    
    def test_deactivate_user(self, client: TestClient, admin_auth_headers: dict, test_user: User):
        """Test deactivating a user."""
        update_data = {
            "is_active": False
        }
        
        response = client.put(
            f"/api/v1/admin/users/{test_user.id}",
            json=update_data,
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["is_active"] is False
    
    def test_delete_user(self, client: TestClient, admin_auth_headers: dict, db_session: Session):
        """Test deleting a user."""
        # Create a user to delete
        user_to_delete = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            full_name="Delete Me",
            subscription_tier=SubscriptionTier.FREE
        )
        db_session.add(user_to_delete)
        db_session.commit()
        db_session.refresh(user_to_delete)
        
        response = client.delete(f"/api/v1/admin/users/{user_to_delete.id}", headers=admin_auth_headers)
        assert response.status_code == 200
        
        # Verify user is deleted
        deleted_user = db_session.query(User).filter(User.id == user_to_delete.id).first()
        assert deleted_user is None


class TestPredictionManagement:
    """Test admin prediction management."""
    
    def test_get_all_predictions(self, client: TestClient, admin_auth_headers: dict):
        """Test getting all predictions."""
        response = client.get("/api/v1/admin/predictions", headers=admin_auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "predictions" in data
        assert isinstance(data["predictions"], list)
    
    def test_get_predictions_with_pagination(self, client: TestClient, admin_auth_headers: dict):
        """Test getting predictions with pagination."""
        response = client.get("/api/v1/admin/predictions?limit=10&offset=0", headers=admin_auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "predictions" in data
        assert len(data["predictions"]) <= 10
    
    def test_get_predictions_by_user(self, client: TestClient, admin_auth_headers: dict, test_user: User):
        """Test getting predictions by specific user."""
        response = client.get(f"/api/v1/admin/predictions?user_id={test_user.id}", headers=admin_auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "predictions" in data
        
        # All predictions should belong to the specified user
        for prediction in data["predictions"]:
            assert prediction["user_id"] == test_user.id
    
    def test_delete_prediction(self, client: TestClient, admin_auth_headers: dict, db_session: Session):
        """Test deleting a prediction."""
        from app.models.prediction import Prediction
        
        # Create a prediction to delete
        from app.models.prediction import AssetType, PredictionStatus
        prediction = Prediction(
            user_id=1,
            symbol="TEST",
            asset_type=AssetType.STOCK,
            days_ahead=30,
            current_price=100.0,
            confidence_score=0.8,
            model_version="test_v1.0",
            status=PredictionStatus.COMPLETED
        )
        db_session.add(prediction)
        db_session.commit()
        db_session.refresh(prediction)
        
        response = client.delete(f"/api/v1/admin/predictions/{prediction.id}", headers=admin_auth_headers)
        assert response.status_code == 200
        
        # Verify prediction is deleted
        deleted_prediction = db_session.query(Prediction).filter(Prediction.id == prediction.id).first()
        assert deleted_prediction is None


class TestSystemMonitoring:
    """Test admin system monitoring functionality."""
    
    def test_get_system_health(self, client: TestClient, admin_auth_headers: dict):
        """Test getting system health status."""
        response = client.get("/api/v1/admin/health", headers=admin_auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        expected_fields = ["status", "database", "external_apis", "memory_usage", "uptime"]
        
        for field in expected_fields:
            assert field in data
    
    def test_database_health_check(self, client: TestClient, admin_auth_headers: dict):
        """Test database health check."""
        response = client.get("/api/v1/admin/health", headers=admin_auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["database"]["status"] in ["healthy", "unhealthy"]
        assert "connection_count" in data["database"]
    
    def test_external_api_health_check(self, client: TestClient, admin_auth_headers: dict):
        """Test external API health check."""
        response = client.get("/api/v1/admin/health", headers=admin_auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        external_apis = data["external_apis"]
        
        expected_apis = ["yfinance", "coingecko"]
        for api in expected_apis:
            assert api in external_apis
            assert external_apis[api]["status"] in ["healthy", "unhealthy"]


class TestAdminSecurity:
    """Test admin security features."""
    
    def test_admin_action_logging(self, client: TestClient, admin_auth_headers: dict, test_user: User):
        """Test that admin actions are logged."""
        # Perform an admin action
        update_data = {"subscription_tier": "basic"}
        response = client.put(
            f"/api/v1/admin/users/{test_user.id}",
            json=update_data,
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        
        # Check if action was logged (this would require implementing audit logging)
        # For now, we just verify the action succeeded
        assert response.json()["subscription_tier"] == "basic"
    
    def test_admin_rate_limiting(self, client: TestClient, admin_auth_headers: dict):
        """Test that admin endpoints have appropriate rate limiting."""
        # Make multiple requests to admin endpoint
        responses = []
        for i in range(10):
            response = client.get("/api/v1/admin/stats", headers=admin_auth_headers)
            responses.append(response)
        
        # All should succeed (admin might have higher limits)
        successful_responses = [r for r in responses if r.status_code == 200]
        assert len(successful_responses) > 0
    
    def test_sensitive_data_protection(self, client: TestClient, admin_auth_headers: dict):
        """Test that sensitive data is protected in admin responses."""
        response = client.get("/api/v1/admin/users", headers=admin_auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        users = data["users"]
        
        # Check that sensitive fields are not exposed
        for user in users:
            assert "hashed_password" not in user
            assert "password" not in user
    
    def test_admin_permission_validation(self, client: TestClient, auth_headers: dict):
        """Test that admin permissions are properly validated."""
        # Try to access admin endpoint with regular user
        admin_endpoints = [
            "/api/v1/admin/stats",
            "/api/v1/admin/users",
            "/api/v1/admin/predictions",
            "/api/v1/admin/health"
        ]
        
        for endpoint in admin_endpoints:
            response = client.get(endpoint, headers=auth_headers)
            assert response.status_code == 403
            assert "Admin access required" in response.json()["detail"]
