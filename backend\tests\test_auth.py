"""
Tests for authentication functionality
"""

import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.orm import Session

from app.core.auth import verify_password, get_password_hash, create_access_token, verify_token
from app.models.user import User, SubscriptionTier


class TestPasswordHashing:
    """Test password hashing and verification."""
    
    def test_password_hashing(self):
        """Test password hashing works correctly."""
        password = "testpassword123"
        hashed = get_password_hash(password)
        
        assert hashed != password
        assert verify_password(password, hashed) is True
        assert verify_password("wrongpassword", hashed) is False
    
    def test_different_passwords_different_hashes(self):
        """Test that different passwords produce different hashes."""
        password1 = "password1"
        password2 = "password2"
        
        hash1 = get_password_hash(password1)
        hash2 = get_password_hash(password2)
        
        assert hash1 != hash2


class TestTokenGeneration:
    """Test JWT token generation and verification."""
    
    def test_create_access_token(self):
        """Test access token creation."""
        data = {"sub": "<EMAIL>"}
        token = create_access_token(data)
        
        assert token is not None
        assert isinstance(token, str)
        assert len(token) > 0
    
    def test_verify_token(self):
        """Test token verification."""
        data = {"sub": "<EMAIL>"}
        token = create_access_token(data)

        email = verify_token(token)
        assert email is not None
        assert email == "<EMAIL>"
    
    def test_invalid_token(self):
        """Test invalid token handling."""
        invalid_token = "invalid.token.here"
        payload = verify_token(invalid_token)
        assert payload is None


class TestAuthEndpoints:
    """Test authentication endpoints."""
    
    def test_register_user(self, client: TestClient):
        """Test user registration."""
        user_data = {
            "email": "<EMAIL>",
            "password": "newpassword123",
            "full_name": "New User"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["email"] == user_data["email"]
        assert data["full_name"] == user_data["full_name"]
        assert "id" in data
        assert "hashed_password" not in data
    
    def test_register_duplicate_email(self, client: TestClient, test_user: User):
        """Test registration with duplicate email fails."""
        user_data = {
            "email": test_user.email,
            "password": "newpassword123",
            "full_name": "Duplicate User"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 400
        assert "already registered" in response.json()["detail"]
    
    def test_login_success(self, client: TestClient, test_user: User):
        """Test successful login."""
        login_data = {
            "username": test_user.email,
            "password": "testpassword123"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 200
        
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
    
    def test_login_invalid_credentials(self, client: TestClient, test_user: User):
        """Test login with invalid credentials."""
        login_data = {
            "username": test_user.email,
            "password": "wrongpassword"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 401
        assert "Incorrect email or password" in response.json()["detail"]
    
    def test_login_nonexistent_user(self, client: TestClient):
        """Test login with nonexistent user."""
        login_data = {
            "username": "<EMAIL>",
            "password": "somepassword"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 401
    
    def test_get_current_user(self, client: TestClient, auth_headers: dict):
        """Test getting current user information."""
        response = client.get("/api/v1/auth/me", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["email"] == "<EMAIL>"
        assert data["full_name"] == "Test User"
        assert data["subscription_tier"] == "free"
    
    def test_get_current_user_unauthorized(self, client: TestClient):
        """Test getting current user without authentication."""
        response = client.get("/api/v1/auth/me")
        assert response.status_code == 401
    
    def test_get_current_user_invalid_token(self, client: TestClient):
        """Test getting current user with invalid token."""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/v1/auth/me", headers=headers)
        assert response.status_code == 401


class TestAuthenticationFlow:
    """Test complete authentication flow."""
    
    def test_complete_auth_flow(self, client: TestClient):
        """Test complete registration -> login -> access flow."""
        # 1. Register
        user_data = {
            "email": "<EMAIL>",
            "password": "flowpassword123",
            "full_name": "Flow Test User"
        }
        
        register_response = client.post("/api/v1/auth/register", json=user_data)
        assert register_response.status_code == 200
        
        # 2. Login
        login_data = {
            "username": user_data["email"],
            "password": user_data["password"]
        }
        
        login_response = client.post("/api/v1/auth/login", data=login_data)
        assert login_response.status_code == 200
        
        token = login_response.json()["access_token"]
        
        # 3. Access protected endpoint
        headers = {"Authorization": f"Bearer {token}"}
        me_response = client.get("/api/v1/auth/me", headers=headers)
        assert me_response.status_code == 200
        
        user_info = me_response.json()
        assert user_info["email"] == user_data["email"]
        assert user_info["full_name"] == user_data["full_name"]


class TestPasswordValidation:
    """Test password validation requirements."""
    
    def test_weak_password_rejection(self, client: TestClient):
        """Test that weak passwords are rejected."""
        weak_passwords = [
            "123",           # Too short
            "password",      # Too common
            "12345678",      # Only numbers
            "abcdefgh",      # Only letters
        ]
        
        for weak_password in weak_passwords:
            user_data = {
                "email": f"test_{weak_password}@example.com",
                "password": weak_password,
                "full_name": "Test User"
            }
            
            response = client.post("/api/v1/auth/register", json=user_data)
            # Note: This test assumes password validation is implemented
            # If not implemented yet, this test will need to be updated
            if response.status_code == 400:
                assert "password" in response.json()["detail"].lower()


class TestEmailValidation:
    """Test email validation."""
    
    def test_invalid_email_formats(self, client: TestClient):
        """Test that invalid email formats are rejected."""
        invalid_emails = [
            "notanemail",
            "@example.com",
            "test@",
            "<EMAIL>",
            "test@example",
        ]
        
        for invalid_email in invalid_emails:
            user_data = {
                "email": invalid_email,
                "password": "validpassword123",
                "full_name": "Test User"
            }
            
            response = client.post("/api/v1/auth/register", json=user_data)
            assert response.status_code == 422  # Validation error


class TestUserActivation:
    """Test user activation and deactivation."""
    
    def test_inactive_user_cannot_login(self, client: TestClient, db_session: Session):
        """Test that inactive users cannot login."""
        # Create inactive user
        user = User(
            email="<EMAIL>",
            hashed_password=get_password_hash("password123"),
            full_name="Inactive User",
            is_active=False,
            subscription_tier=SubscriptionTier.FREE
        )
        db_session.add(user)
        db_session.commit()
        
        # Try to login
        login_data = {
            "username": "<EMAIL>",
            "password": "password123"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 401
        assert "inactive" in response.json()["detail"].lower()


class TestTokenExpiration:
    """Test token expiration handling."""
    
    def test_expired_token_handling(self):
        """Test handling of expired tokens."""
        # Create token with very short expiration
        from datetime import timedelta
        data = {"sub": "<EMAIL>"}
        
        # This would require modifying create_access_token to accept custom expiration
        # For now, we'll test the concept
        token = create_access_token(data, expires_delta=timedelta(seconds=-1))
        
        # Verify expired token
        payload = verify_token(token)
        # Should return None for expired token
        assert payload is None or "exp" in payload


class TestSecurityHeaders:
    """Test security-related headers and responses."""
    
    def test_no_password_in_response(self, client: TestClient):
        """Test that passwords are never included in responses."""
        user_data = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "full_name": "Security Test"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 200
        
        data = response.json()
        assert "password" not in data
        assert "hashed_password" not in data
    
    def test_login_response_structure(self, client: TestClient, test_user: User):
        """Test that login response has correct structure."""
        login_data = {
            "username": test_user.email,
            "password": "testpassword123"
        }
        
        response = client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 200
        
        data = response.json()
        required_fields = ["access_token", "token_type"]
        for field in required_fields:
            assert field in data
        
        # Should not contain sensitive information
        sensitive_fields = ["password", "hashed_password", "secret"]
        for field in sensitive_fields:
            assert field not in data
