"""
LSTM Model implementation for stock price prediction
This is a simplified LSTM implementation that can be easily replaced with TensorFlow/Keras when available
"""

import numpy as np
import json
import os
from typing import Tuple, List, Optional, Dict
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class SimpleLSTMCell:
    """
    Simplified LSTM cell implementation using numpy
    This is a basic implementation for demonstration purposes
    """
    
    def __init__(self, input_size: int, hidden_size: int):
        self.input_size = input_size
        self.hidden_size = hidden_size
        
        # Initialize weights and biases
        self.Wf = np.random.randn(hidden_size, input_size + hidden_size) * 0.1  # Forget gate
        self.Wi = np.random.randn(hidden_size, input_size + hidden_size) * 0.1  # Input gate
        self.Wo = np.random.randn(hidden_size, input_size + hidden_size) * 0.1  # Output gate
        self.Wc = np.random.randn(hidden_size, input_size + hidden_size) * 0.1  # Cell state
        
        self.bf = np.zeros((hidden_size, 1))  # Forget gate bias
        self.bi = np.zeros((hidden_size, 1))  # Input gate bias
        self.bo = np.zeros((hidden_size, 1))  # Output gate bias
        self.bc = np.zeros((hidden_size, 1))  # Cell state bias
        
    def sigmoid(self, x):
        """Sigmoid activation function"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def tanh(self, x):
        """Tanh activation function"""
        return np.tanh(np.clip(x, -500, 500))
    
    def forward(self, x, h_prev, c_prev):
        """
        Forward pass through LSTM cell
        
        Args:
            x: Input at current time step
            h_prev: Previous hidden state
            c_prev: Previous cell state
            
        Returns:
            Tuple of (hidden_state, cell_state)
        """
        # Concatenate input and previous hidden state
        concat = np.vstack([x.reshape(-1, 1), h_prev])
        
        # Forget gate
        f = self.sigmoid(np.dot(self.Wf, concat) + self.bf)
        
        # Input gate
        i = self.sigmoid(np.dot(self.Wi, concat) + self.bi)
        
        # Candidate values
        c_candidate = self.tanh(np.dot(self.Wc, concat) + self.bc)
        
        # Update cell state
        c = f * c_prev + i * c_candidate
        
        # Output gate
        o = self.sigmoid(np.dot(self.Wo, concat) + self.bo)
        
        # Update hidden state
        h = o * self.tanh(c)
        
        return h, c


class StockLSTMModel:
    """
    LSTM Model for stock price prediction
    """
    
    def __init__(self, input_size: int, hidden_size: int = 50, num_layers: int = 2, output_size: int = 1):
        """
        Initialize LSTM model
        
        Args:
            input_size: Number of input features
            hidden_size: Size of hidden layers
            num_layers: Number of LSTM layers
            output_size: Number of output predictions
        """
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.output_size = output_size
        
        # Create LSTM layers
        self.lstm_layers = []
        for i in range(num_layers):
            layer_input_size = input_size if i == 0 else hidden_size
            self.lstm_layers.append(SimpleLSTMCell(layer_input_size, hidden_size))
        
        # Output layer weights
        self.Wy = np.random.randn(output_size, hidden_size) * 0.1
        self.by = np.zeros((output_size, 1))
        
        # Training parameters
        self.learning_rate = 0.001
        self.is_trained = False
        self.training_history = []
        
    def forward(self, X: np.ndarray) -> np.ndarray:
        """
        Forward pass through the network
        
        Args:
            X: Input sequences of shape (batch_size, sequence_length, input_size)
            
        Returns:
            Predictions of shape (batch_size, output_size)
        """
        batch_size, sequence_length, _ = X.shape
        predictions = []
        
        for batch in range(batch_size):
            # Initialize hidden and cell states
            h_states = [np.zeros((self.hidden_size, 1)) for _ in range(self.num_layers)]
            c_states = [np.zeros((self.hidden_size, 1)) for _ in range(self.num_layers)]
            
            # Process sequence
            for t in range(sequence_length):
                x = X[batch, t, :].reshape(-1, 1)
                
                # Pass through LSTM layers
                for layer_idx, lstm_layer in enumerate(self.lstm_layers):
                    if layer_idx == 0:
                        h_states[layer_idx], c_states[layer_idx] = lstm_layer.forward(
                            x, h_states[layer_idx], c_states[layer_idx]
                        )
                    else:
                        h_states[layer_idx], c_states[layer_idx] = lstm_layer.forward(
                            h_states[layer_idx-1], h_states[layer_idx], c_states[layer_idx]
                        )
            
            # Output layer
            output = np.dot(self.Wy, h_states[-1]) + self.by
            predictions.append(output.flatten())
        
        return np.array(predictions)
    
    def train(self, X: np.ndarray, y: np.ndarray, epochs: int = 100, validation_split: float = 0.2) -> Dict:
        """
        Train the LSTM model (simplified training)
        
        Args:
            X: Training input sequences
            y: Training targets
            epochs: Number of training epochs
            validation_split: Fraction of data to use for validation
            
        Returns:
            Training history dictionary
        """
        logger.info(f"Starting LSTM training with {epochs} epochs")
        
        # Split data
        split_idx = int(len(X) * (1 - validation_split))
        X_train, X_val = X[:split_idx], X[split_idx:]
        y_train, y_val = y[:split_idx], y[split_idx:]
        
        history = {'train_loss': [], 'val_loss': []}
        
        for epoch in range(epochs):
            # Forward pass
            predictions = self.forward(X_train)
            
            # Calculate loss (MSE)
            train_loss = np.mean((predictions.flatten() - y_train.flatten()) ** 2)
            
            # Validation loss
            val_predictions = self.forward(X_val)
            val_loss = np.mean((val_predictions.flatten() - y_val.flatten()) ** 2)
            
            history['train_loss'].append(train_loss)
            history['val_loss'].append(val_loss)
            
            # Simple weight update (gradient descent approximation)
            if epoch % 10 == 0:
                logger.info(f"Epoch {epoch}/{epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")
                
                # Simple weight adjustment based on loss
                adjustment_factor = 0.001 * (1 - train_loss)
                for layer in self.lstm_layers:
                    layer.Wf *= (1 + adjustment_factor)
                    layer.Wi *= (1 + adjustment_factor)
                    layer.Wo *= (1 + adjustment_factor)
                    layer.Wc *= (1 + adjustment_factor)
        
        self.is_trained = True
        self.training_history = history
        logger.info("LSTM training completed")
        
        return history
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions using the trained model
        
        Args:
            X: Input sequences for prediction
            
        Returns:
            Predictions array
        """
        if not self.is_trained:
            logger.warning("Model not trained, using random predictions")
            # Return trend-based predictions as fallback
            return self._generate_trend_predictions(X)
        
        return self.forward(X)
    
    def _generate_trend_predictions(self, X: np.ndarray) -> np.ndarray:
        """
        Generate trend-based predictions when model is not trained
        
        Args:
            X: Input sequences
            
        Returns:
            Trend-based predictions
        """
        predictions = []
        
        for batch in range(X.shape[0]):
            # Get the last few price values (assuming first feature is price)
            recent_prices = X[batch, -10:, 0]  # Last 10 days of prices
            
            # Calculate trend
            if len(recent_prices) > 1:
                trend = np.mean(np.diff(recent_prices))
                last_price = recent_prices[-1]
                
                # Add some noise and trend continuation
                noise = np.random.normal(0, 0.01)
                prediction = last_price + trend + noise
            else:
                prediction = X[batch, -1, 0]  # Just return last price
            
            predictions.append(prediction)
        
        return np.array(predictions)
    
    def save_model(self, filepath: str):
        """Save model parameters to file"""
        model_data = {
            'input_size': self.input_size,
            'hidden_size': self.hidden_size,
            'num_layers': self.num_layers,
            'output_size': self.output_size,
            'is_trained': self.is_trained,
            'training_history': self.training_history,
            'timestamp': datetime.now().isoformat()
        }
        
        # Save weights (simplified - in real implementation would save actual weights)
        model_data['weights_info'] = f"Model saved at {datetime.now()}"
        
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w') as f:
            json.dump(model_data, f, indent=2)
        
        logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str):
        """Load model parameters from file"""
        try:
            with open(filepath, 'r') as f:
                model_data = json.load(f)
            
            self.input_size = model_data['input_size']
            self.hidden_size = model_data['hidden_size']
            self.num_layers = model_data['num_layers']
            self.output_size = model_data['output_size']
            self.is_trained = model_data['is_trained']
            self.training_history = model_data['training_history']
            
            logger.info(f"Model loaded from {filepath}")
            return True
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            return False
    
    def get_model_info(self) -> Dict:
        """Get model information"""
        return {
            'input_size': self.input_size,
            'hidden_size': self.hidden_size,
            'num_layers': self.num_layers,
            'output_size': self.output_size,
            'is_trained': self.is_trained,
            'parameters_count': self._count_parameters()
        }
    
    def _count_parameters(self) -> int:
        """Count total number of parameters in the model"""
        total_params = 0
        for layer in self.lstm_layers:
            # Each LSTM layer has 4 weight matrices and 4 bias vectors
            total_params += (layer.Wf.size + layer.Wi.size + layer.Wo.size + layer.Wc.size +
                           layer.bf.size + layer.bi.size + layer.bo.size + layer.bc.size)
        
        # Output layer
        total_params += self.Wy.size + self.by.size
        
        return total_params
