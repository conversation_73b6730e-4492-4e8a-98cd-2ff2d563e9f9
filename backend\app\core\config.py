"""
Core configuration settings for TradingBot SAAS Platform
"""

import secrets
from typing import List, Optional, Union
from pydantic import AnyHttpUrl, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # Project Info
    PROJECT_NAME: str = "TradingBot SAAS ML Platform"
    API_V1_STR: str = "/api/v1"
    
    # Security
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    ALGORITHM: str = "HS256"
    
    # CORS
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = [
        "http://localhost:3000",  # React dev server
        "http://localhost:5173",  # Vite dev server
        "http://localhost:8000",  # FastAPI dev server
    ]
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # Trusted Hosts
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1", "*"]
    
    # Database
    DATABASE_TYPE: str = "sqlite"  # "sqlite" or "postgresql"
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "tradingbot"
    POSTGRES_PASSWORD: str = "password"
    POSTGRES_DB: str = "tradingbot_db"
    POSTGRES_PORT: str = "5432"
    SQLITE_DB_PATH: str = "data/tradingbot.db"

    @property
    def SQLALCHEMY_DATABASE_URI(self) -> str:
        if self.DATABASE_TYPE == "sqlite":
            return f"sqlite:///{self.SQLITE_DB_PATH}"
        else:
            return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"
    
    # Redis
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    
    @property
    def REDIS_URL(self) -> str:
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    # External APIs
    ALPHA_VANTAGE_API_KEY: Optional[str] = None
    COINGECKO_API_KEY: Optional[str] = None
    
    # ML Model Settings
    MODEL_CACHE_TTL: int = 3600  # 1 hour
    PREDICTION_CACHE_TTL: int = 300  # 5 minutes
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    PREMIUM_RATE_LIMIT_PER_MINUTE: int = 300
    
    # File Storage
    UPLOAD_DIR: str = "uploads"
    MODEL_DIR: str = "models"
    DATA_DIR: str = "data"
    
    class Config:
        case_sensitive = True
        env_file = ".env"


settings = Settings()
