"""
Prediction database models
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Text, ForeignKey, Enum, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from app.core.database import Base


class AssetType(enum.Enum):
    STOCK = "stock"
    CRYPTO = "crypto"


class PredictionStatus(enum.Enum):
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"


class Prediction(Base):
    __tablename__ = "predictions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    symbol = Column(String, nullable=False, index=True)
    asset_type = Column(Enum(AssetType), nullable=False)
    days_ahead = Column(Integer, nullable=False)
    current_price = Column(Float, nullable=True)
    predicted_prices = Column(JSON, nullable=True)  # List of predicted prices
    prediction_dates = Column(JSON, nullable=True)  # List of prediction dates
    confidence_score = Column(Float, nullable=True)
    model_version = Column(String, nullable=False)
    status = Column(Enum(PredictionStatus), default=PredictionStatus.PENDING)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationship to user
    user = relationship("User", back_populates="predictions")

    # Relationship to accuracy records
    accuracy_records = relationship("PredictionAccuracy", back_populates="prediction")

    def __repr__(self):
        return f"<Prediction(symbol='{self.symbol}', type='{self.asset_type.value}', status='{self.status.value}')>"


class PredictionAccuracy(Base):
    __tablename__ = "prediction_accuracy"

    id = Column(Integer, primary_key=True, index=True)
    prediction_id = Column(Integer, ForeignKey("predictions.id"), nullable=False)
    actual_price = Column(Float, nullable=False)
    predicted_price = Column(Float, nullable=False)
    accuracy_percentage = Column(Float, nullable=False)
    evaluation_date = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship to prediction
    prediction = relationship("Prediction", back_populates="accuracy_records")

    def __repr__(self):
        return f"<PredictionAccuracy(prediction_id={self.prediction_id}, accuracy={self.accuracy_percentage}%)>"
