#!/usr/bin/env python3
"""Test real prediction functionality"""

import asyncio

async def test_prediction():
    print('=== TESTING REAL PREDICTION FUNCTIONALITY ===')
    print()
    
    try:
        from app.services.stock_service import StockService
        service = StockService()
        
        print('1. Testing Real Stock Prediction:')
        print('   (This may take a moment for LSTM training...)')
        
        # Test with a simple prediction
        result = await service.predict_stock_price('AAPL', 3)
        
        print(f'✅ Prediction completed successfully!')
        print(f'   Symbol: {result["symbol"]}')
        print(f'   Current Price: ${result["current_price"]:.2f}')
        print(f'   Predicted Prices: {[f"${p:.2f}" for p in result["predicted_prices"]]}')
        print(f'   Model Type: {result.get("model_type", "Unknown")}')
        print(f'   Model Version: {result["model_version"]}')
        print(f'   Confidence: {result["confidence_score"]:.2f}')
        print(f'   Note: {result.get("note", "N/A")}')
        print()
        
        # Determine if it's real or fallback
        if 'PYTORCH' in result.get("model_type", ""):
            print('🎉 SUCCESS: Using REAL PyTorch LSTM predictions!')
        elif 'trend' in result.get("model_version", "").lower():
            print('⚠️  Using real trend analysis (fallback - but still REAL data)')
        else:
            print('❓ Unknown prediction type')
        
        # Verify it's not the old mock data
        if result["current_price"] != 150.0:
            print('✅ Confirmed: NOT using old mock data ($150)')
        else:
            print('❌ Warning: Still using old mock data')
            
    except Exception as e:
        print(f'❌ Error during prediction: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_prediction())
