#!/usr/bin/env python3
"""Create test users for the application"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal, engine
from app.core.database import Base
from app.models.user import User, SubscriptionTier
from app.core.auth import get_password_hash

def create_test_users():
    """Create test users for the application"""
    
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    # Create database session
    db = SessionLocal()
    
    try:
        # Check if users already exist
        existing_users = db.query(User).all()
        print(f"Found {len(existing_users)} existing users")
        
        # Create demo user if doesn't exist (matches frontend credentials)
        demo_email = "<EMAIL>"
        existing_demo = db.query(User).filter(User.email == demo_email).first()

        if not existing_demo:
            demo_user = User(
                email=demo_email,
                hashed_password=get_password_hash("demo123"),
                full_name="Demo User",
                is_active=True,
                is_verified=True,
                subscription_tier=SubscriptionTier.FREE
            )
            db.add(demo_user)
            print(f"✅ Created demo user: {demo_email} / demo123")
        else:
            print(f"✅ Demo user already exists: {demo_email}")

        # Create test user if doesn't exist
        test_email = "<EMAIL>"
        existing_test = db.query(User).filter(User.email == test_email).first()

        if not existing_test:
            test_user = User(
                email=test_email,
                hashed_password=get_password_hash("test123"),
                full_name="Test User",
                is_active=True,
                is_verified=True,
                subscription_tier=SubscriptionTier.FREE
            )
            db.add(test_user)
            print(f"✅ Created test user: {test_email} / test123")
        else:
            print(f"✅ Test user already exists: {test_email}")
        
        # Create admin user if doesn't exist
        admin_email = "<EMAIL>"
        existing_admin = db.query(User).filter(User.email == admin_email).first()
        
        if not existing_admin:
            admin_user = User(
                email=admin_email,
                hashed_password=get_password_hash("admin123"),
                full_name="Admin User",
                is_active=True,
                is_verified=True,
                subscription_tier=SubscriptionTier.ENTERPRISE
            )
            db.add(admin_user)
            print(f"✅ Created admin user: {admin_email} / admin123")
        else:
            print(f"✅ Admin user already exists: {admin_email}")
        
        # Create premium user if doesn't exist
        premium_email = "<EMAIL>"
        existing_premium = db.query(User).filter(User.email == premium_email).first()
        
        if not existing_premium:
            premium_user = User(
                email=premium_email,
                hashed_password=get_password_hash("premium123"),
                full_name="Premium User",
                is_active=True,
                is_verified=True,
                subscription_tier=SubscriptionTier.PREMIUM
            )
            db.add(premium_user)
            print(f"✅ Created premium user: {premium_email} / premium123")
        else:
            print(f"✅ Premium user already exists: {premium_email}")
        
        # Commit changes
        db.commit()
        
        # List all users
        all_users = db.query(User).all()
        print(f"\n📋 All users in database ({len(all_users)}):")
        for user in all_users:
            print(f"   - {user.email} ({user.subscription_tier.value}) - Active: {user.is_active}")
        
        print(f"\n🎉 Database setup complete!")
        print(f"🔑 You can now login with:")
        print(f"   - <EMAIL> / demo123 (FREE tier) - Frontend Demo")
        print(f"   - <EMAIL> / test123 (FREE tier)")
        print(f"   - <EMAIL> / premium123 (PREMIUM tier)")
        print(f"   - <EMAIL> / admin123 (ENTERPRISE tier)")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_test_users()
