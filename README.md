# TradingBot - SAAS ML Price Prediction Platform

A comprehensive SAAS platform for predicting stock and cryptocurrency prices using LSTM neural networks.

## Features

- **Stock Price Prediction**: Advanced LSTM models for stock market analysis
- **Cryptocurrency Prediction**: Specialized models for crypto market volatility
- **Real-time Data**: Live market data integration
- **User Dashboard**: Interactive charts and prediction visualization
- **API Access**: RESTful API for programmatic access
- **Subscription Management**: Tiered access with rate limiting

## Architecture

```
TradingBot/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core configurations
│   │   ├── models/         # Database models
│   │   ├── services/       # Business logic
│   │   └── ml/             # ML modules
│   │       ├── stocks/     # Stock prediction module
│   │       └── crypto/     # Crypto prediction module
│   ├── tests/              # Backend tests
│   └── requirements.txt
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   └── utils/          # Utilities
│   └── package.json
├── data/                   # Data storage
├── models/                 # Trained ML models
├── docker-compose.yml      # Container orchestration
└── README.md
```

## Tech Stack

### Backend
- **FastAPI**: Modern Python web framework
- **TensorFlow/Keras**: LSTM model implementation
- **PostgreSQL**: Primary database
- **Redis**: Caching and session management
- **Celery**: Background task processing

### Frontend
- **React**: User interface
- **TypeScript**: Type safety
- **Chart.js**: Data visualization
- **Tailwind CSS**: Styling

### Data Sources
- **Alpha Vantage**: Stock market data
- **CoinGecko API**: Cryptocurrency data
- **Yahoo Finance**: Additional market data

## Getting Started

1. Clone the repository
2. Set up the backend environment
3. Install frontend dependencies
4. Configure environment variables
5. Run the development servers

## License

MIT License
