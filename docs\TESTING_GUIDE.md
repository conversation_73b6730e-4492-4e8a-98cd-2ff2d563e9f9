# Testing Guide - SAAS ML Trading Bot

## Overview

This guide covers the comprehensive testing strategy for the SAAS ML Trading Bot, including unit tests, integration tests, and performance testing.

## Test Structure

```
backend/tests/
├── __init__.py
├── conftest.py              # Test configuration and fixtures
├── test_auth.py            # Authentication tests
├── test_subscriptions.py   # Subscription management tests
├── test_rate_limiting.py   # Rate limiting tests
├── test_predictions.py     # Prediction functionality tests
├── test_admin.py          # Admin functionality tests
└── pytest.ini            # Pytest configuration
```

## Prerequisites

### Install Testing Dependencies

```bash
cd backend
pip install pytest pytest-asyncio httpx pytest-cov
```

### Environment Setup

Create a test environment file:

```bash
# .env.test
TESTING=true
SECRET_KEY=test-secret-key-for-testing-only
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
DATABASE_URL=sqlite:///./test.db
```

## Running Tests

### Run All Tests

```bash
cd backend
pytest
```

### Run Specific Test Categories

```bash
# Authentication tests
pytest tests/test_auth.py

# Subscription tests
pytest tests/test_subscriptions.py

# Rate limiting tests
pytest tests/test_rate_limiting.py

# Prediction tests
pytest tests/test_predictions.py

# Admin tests
pytest tests/test_admin.py
```

### Run Tests with Coverage

```bash
pytest --cov=app --cov-report=html --cov-report=term-missing
```

### Run Tests with Markers

```bash
# Run only unit tests
pytest -m unit

# Run only integration tests
pytest -m integration

# Skip slow tests
pytest -m "not slow"
```

## Test Categories

### 1. Authentication Tests (`test_auth.py`)

**Coverage:**
- Password hashing and verification
- JWT token generation and validation
- User registration and login
- Authentication flow
- Password validation
- Email validation
- User activation/deactivation
- Token expiration handling
- Security headers

**Key Test Cases:**
```python
def test_password_hashing()
def test_create_access_token()
def test_register_user()
def test_login_success()
def test_get_current_user()
def test_complete_auth_flow()
```

### 2. Subscription Tests (`test_subscriptions.py`)

**Coverage:**
- Subscription service functionality
- Tier configuration validation
- API call and prediction limits
- Feature access control
- Usage tracking and statistics
- Subscription upgrades/downgrades
- Payment integration hooks

**Key Test Cases:**
```python
def test_validate_api_call_within_limit()
def test_validate_prediction_exceeds_days_limit()
def test_upgrade_subscription()
def test_get_usage_stats()
def test_tier_feature_progression()
```

### 3. Rate Limiting Tests (`test_rate_limiting.py`)

**Coverage:**
- Rate limiter core functionality
- Sliding window algorithm
- Memory cleanup
- Different subscription tier limits
- Rate limit headers
- Performance characteristics

**Key Test Cases:**
```python
def test_rate_limiter_allows_within_limit()
def test_rate_limiter_blocks_over_limit()
def test_rate_limiter_sliding_window()
def test_rate_limit_different_tiers()
def test_rate_limiter_performance()
```

### 4. Prediction Tests (`test_predictions.py`)

**Coverage:**
- Stock and crypto prediction services
- Data fetching and preprocessing
- LSTM model creation and training
- Prediction accuracy calculation
- Error handling for network issues
- Prediction caching
- Database integration

**Key Test Cases:**
```python
def test_get_stock_data()
def test_preprocess_data()
def test_predict_stock_price()
def test_predict_crypto_price()
def test_prediction_accuracy_calculation()
def test_network_error_handling()
```

### 5. Admin Tests (`test_admin.py`)

**Coverage:**
- Admin access control
- System statistics
- User management
- Prediction management
- System health monitoring
- Security features

**Key Test Cases:**
```python
def test_admin_access_with_admin_user()
def test_get_admin_stats()
def test_get_all_users()
def test_update_user_subscription()
def test_get_system_health()
```

## Test Fixtures

### Database Fixtures

```python
@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test."""
    # Creates isolated test database

@pytest.fixture
def test_user(db_session) -> User:
    """Create a test user."""
    # Returns a FREE tier user

@pytest.fixture
def premium_user(db_session) -> User:
    """Create a premium test user."""
    # Returns a PREMIUM tier user
```

### Authentication Fixtures

```python
@pytest.fixture
def auth_headers(test_user) -> dict:
    """Create authentication headers for test user."""
    # Returns headers with JWT token

@pytest.fixture
def admin_auth_headers(admin_user) -> dict:
    """Create authentication headers for admin user."""
    # Returns admin headers
```

### Mock Data Fixtures

```python
@pytest.fixture
def mock_yfinance_data():
    """Mock yfinance data."""
    # Returns sample stock data

@pytest.fixture
def mock_coingecko_data():
    """Mock CoinGecko data."""
    # Returns sample crypto data
```

## Mocking External Services

### Stock Data (yfinance)

```python
@patch('app.services.stock_service.yf.download')
def test_get_stock_data(self, mock_download, db_session):
    mock_data = pd.DataFrame({
        'Close': [150.0, 151.0, 149.5],
        'Volume': [1000000, 1100000, 950000]
    })
    mock_download.return_value = mock_data
    # Test implementation
```

### Crypto Data (CoinGecko)

```python
@patch('app.services.crypto_service.requests.get')
def test_get_crypto_data(self, mock_get, db_session):
    mock_response = Mock()
    mock_response.json.return_value = {
        "prices": [[1640995200000, 47000]]
    }
    mock_get.return_value = mock_response
    # Test implementation
```

## Performance Testing

### Rate Limiter Performance

```python
def test_rate_limiter_performance(self, performance_timer):
    rate_limiter = RateLimiter()
    
    performance_timer.start()
    for i in range(1000):
        rate_limiter.is_allowed("user", 1000, 3600)
    performance_timer.stop()
    
    assert performance_timer.elapsed < 1.0
```

### Memory Usage Testing

```python
def test_rate_limiter_memory_usage(self):
    rate_limiter = RateLimiter()
    
    # Create many identifiers
    for i in range(1000):
        rate_limiter.is_allowed(f"user_{i}", 10, 1)
    
    # Verify memory cleanup
    time.sleep(1.1)
    # Check memory doesn't grow unbounded
```

## Integration Testing

### Complete Authentication Flow

```python
def test_complete_auth_flow(self, client: TestClient):
    # 1. Register
    register_response = client.post("/api/v1/auth/register", json=user_data)
    
    # 2. Login
    login_response = client.post("/api/v1/auth/login", data=login_data)
    token = login_response.json()["access_token"]
    
    # 3. Access protected endpoint
    headers = {"Authorization": f"Bearer {token}"}
    me_response = client.get("/api/v1/auth/me", headers=headers)
    
    # Verify complete flow works
```

### Subscription Upgrade Flow

```python
def test_complete_upgrade_flow(self, client: TestClient, auth_headers: dict):
    # 1. Check initial tier
    # 2. Get available tiers
    # 3. Upgrade subscription
    # 4. Verify upgrade
```

## Error Testing

### Network Errors

```python
def test_network_error_handling(self, db_session):
    with patch('app.services.stock_service.yf.download') as mock_download:
        mock_download.side_effect = Exception("Network error")
        
        with pytest.raises(Exception):
            stock_service.get_stock_data("AAPL")
```

### Validation Errors

```python
def test_invalid_email_formats(self, client: TestClient):
    invalid_emails = ["notanemail", "@example.com", "test@"]
    
    for invalid_email in invalid_emails:
        response = client.post("/api/v1/auth/register", json={
            "email": invalid_email,
            "password": "validpassword123"
        })
        assert response.status_code == 422
```

## Security Testing

### Authentication Security

```python
def test_no_password_in_response(self, client: TestClient):
    response = client.post("/api/v1/auth/register", json=user_data)
    data = response.json()
    
    assert "password" not in data
    assert "hashed_password" not in data
```

### Admin Access Control

```python
def test_admin_permission_validation(self, client: TestClient, auth_headers: dict):
    admin_endpoints = ["/api/v1/admin/stats", "/api/v1/admin/users"]
    
    for endpoint in admin_endpoints:
        response = client.get(endpoint, headers=auth_headers)
        assert response.status_code == 403
```

## Continuous Integration

### GitHub Actions Example

```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    - name: Install dependencies
      run: |
        cd backend
        pip install -r requirements.txt
        pip install pytest pytest-asyncio httpx pytest-cov
    - name: Run tests
      run: |
        cd backend
        pytest --cov=app --cov-report=xml
    - name: Upload coverage
      uses: codecov/codecov-action@v1
```

## Test Data Management

### Test Database

- Uses SQLite for fast, isolated testing
- Fresh database for each test function
- Automatic cleanup after tests

### Mock Data

- Consistent mock data across tests
- Realistic data patterns
- Edge cases covered

## Best Practices

1. **Isolation**: Each test is independent
2. **Mocking**: External services are mocked
3. **Coverage**: Aim for >80% code coverage
4. **Performance**: Tests run quickly (<5 minutes total)
5. **Reliability**: Tests are deterministic
6. **Documentation**: Clear test descriptions

## Troubleshooting

### Common Issues

1. **Import Errors**: Check PYTHONPATH and module structure
2. **Database Errors**: Ensure test database is properly isolated
3. **Mock Failures**: Verify mock patches match actual code
4. **Async Issues**: Use proper async fixtures and decorators

### Debug Mode

```bash
# Run with verbose output
pytest -v -s

# Run specific test with debugging
pytest tests/test_auth.py::TestAuthEndpoints::test_login_success -v -s

# Run with pdb debugger
pytest --pdb
```

## Reporting

### Coverage Report

```bash
pytest --cov=app --cov-report=html
# Open htmlcov/index.html in browser
```

### Test Results

```bash
pytest --junitxml=test-results.xml
```

This comprehensive testing strategy ensures the reliability, security, and performance of the SAAS ML Trading Bot system.
