"""
Subscription management service for handling user tiers and API limits
"""

from typing import Dict, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
import logging

from app.models.user import User, SubscriptionTier
from app.services.database_service import DatabaseService

logger = logging.getLogger(__name__)


class SubscriptionService:
    """Service for managing user subscriptions and API limits"""
    
    # Subscription tier configurations
    TIER_CONFIGS = {
        SubscriptionTier.FREE: {
            "api_calls_limit": 100,  # per month
            "predictions_per_day": 5,
            "max_days_ahead": 30,
            "features": ["basic_predictions", "historical_data"],
            "price": 0.0
        },
        SubscriptionTier.BASIC: {
            "api_calls_limit": 1000,  # per month
            "predictions_per_day": 50,
            "max_days_ahead": 90,
            "features": ["basic_predictions", "historical_data", "technical_indicators", "email_alerts"],
            "price": 9.99
        },
        SubscriptionTier.PREMIUM: {
            "api_calls_limit": 10000,  # per month
            "predictions_per_day": 500,
            "max_days_ahead": 365,
            "features": ["basic_predictions", "historical_data", "technical_indicators", "email_alerts", 
                        "advanced_models", "portfolio_analysis", "api_access"],
            "price": 29.99
        },
        SubscriptionTier.ENTERPRISE: {
            "api_calls_limit": 100000,  # per month
            "predictions_per_day": 5000,
            "max_days_ahead": 365,
            "features": ["basic_predictions", "historical_data", "technical_indicators", "email_alerts", 
                        "advanced_models", "portfolio_analysis", "api_access", "priority_support", 
                        "custom_models", "bulk_predictions"],
            "price": 99.99
        }
    }
    
    def __init__(self, db: Session):
        self.db = db
        self.db_service = DatabaseService(db)
    
    def get_user_tier_config(self, user: User) -> Dict:
        """Get configuration for user's subscription tier"""
        return self.TIER_CONFIGS.get(user.subscription_tier, self.TIER_CONFIGS[SubscriptionTier.FREE])

    def get_tier_config(self, tier: SubscriptionTier) -> Dict:
        """Get configuration for a specific subscription tier"""
        return self.TIER_CONFIGS.get(tier, self.TIER_CONFIGS[SubscriptionTier.FREE])
    
    def check_api_limit(self, user: User) -> bool:
        """Check if user has exceeded their API call limit"""
        tier_config = self.get_user_tier_config(user)
        return user.api_calls_used < tier_config["api_calls_limit"]
    
    def increment_api_usage(self, user: User) -> None:
        """Increment user's API usage counter"""
        user.api_calls_used += 1
        self.db.commit()
        self.db.refresh(user)
    
    def check_daily_prediction_limit(self, user: User) -> bool:
        """Check if user has exceeded their daily prediction limit"""
        tier_config = self.get_user_tier_config(user)
        
        # Get today's predictions count
        today = datetime.now().date()
        today_predictions = self.db_service.get_user_predictions(user.id, 1000)
        today_count = len([p for p in today_predictions if p.created_at.date() == today])
        
        return today_count < tier_config["predictions_per_day"]
    
    def check_prediction_days_limit(self, user: User, days_ahead: int) -> bool:
        """Check if requested prediction days are within user's tier limit"""
        tier_config = self.get_user_tier_config(user)
        return days_ahead <= tier_config["max_days_ahead"]
    
    def check_feature_access(self, user: User, feature: str) -> bool:
        """Check if user has access to a specific feature"""
        tier_config = self.get_user_tier_config(user)
        return feature in tier_config["features"]
    
    def upgrade_subscription(self, user: User, new_tier: SubscriptionTier) -> User:
        """Upgrade user's subscription tier"""
        if new_tier.value <= user.subscription_tier.value:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot downgrade or set same tier. Contact support for downgrades."
            )
        
        # Update user's subscription
        old_tier = user.subscription_tier
        user.subscription_tier = new_tier
        
        # Update API limits based on new tier
        new_config = self.TIER_CONFIGS[new_tier]
        user.api_calls_limit = new_config["api_calls_limit"]
        
        # Reset usage if upgrading (optional - could be prorated)
        user.api_calls_used = 0
        
        self.db.commit()
        self.db.refresh(user)
        
        logger.info(f"User {user.email} upgraded from {old_tier.value} to {new_tier.value}")
        return user
    
    def reset_monthly_usage(self, user: User) -> None:
        """Reset user's monthly API usage (called by scheduled job)"""
        user.api_calls_used = 0
        self.db.commit()
        self.db.refresh(user)
        logger.info(f"Reset monthly usage for user {user.email}")
    
    def get_usage_stats(self, user: User) -> Dict:
        """Get user's current usage statistics"""
        tier_config = self.get_user_tier_config(user)
        
        # Get today's predictions
        today = datetime.now().date()
        today_predictions = self.db_service.get_user_predictions(user.id, 1000)
        today_count = len([p for p in today_predictions if p.created_at.date() == today])
        
        # Calculate usage percentages
        api_usage_percent = (user.api_calls_used / tier_config["api_calls_limit"]) * 100
        daily_usage_percent = (today_count / tier_config["predictions_per_day"]) * 100
        
        return {
            "subscription_tier": user.subscription_tier.value,
            "api_calls_used": user.api_calls_used,
            "api_calls_limit": tier_config["api_calls_limit"],
            "api_usage_percent": round(api_usage_percent, 2),
            "daily_predictions_used": today_count,
            "daily_predictions_limit": tier_config["predictions_per_day"],
            "daily_usage_percent": round(daily_usage_percent, 2),
            "max_days_ahead": tier_config["max_days_ahead"],
            "features": tier_config["features"],
            "price": tier_config["price"]
        }
    
    def get_all_tiers(self) -> Dict:
        """Get information about all subscription tiers"""
        return {
            tier.value: {
                "name": tier.value.title(),
                "price": config["price"],
                "api_calls_limit": config["api_calls_limit"],
                "predictions_per_day": config["predictions_per_day"],
                "max_days_ahead": config["max_days_ahead"],
                "features": config["features"]
            }
            for tier, config in self.TIER_CONFIGS.items()
        }
    
    def validate_subscription_access(self, user: User, operation: str, **kwargs) -> None:
        """
        Validate if user can perform an operation based on their subscription
        
        Args:
            user: User object
            operation: Type of operation ('prediction', 'api_call', 'feature_access')
            **kwargs: Additional parameters based on operation type
        """
        if operation == "api_call":
            if not self.check_api_limit(user):
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail=f"API call limit exceeded. Upgrade your subscription or wait for monthly reset."
                )
        
        elif operation == "prediction":
            days_ahead = kwargs.get("days_ahead", 30)
            
            if not self.check_daily_prediction_limit(user):
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="Daily prediction limit exceeded. Upgrade your subscription for more predictions."
                )
            
            if not self.check_prediction_days_limit(user, days_ahead):
                tier_config = self.get_user_tier_config(user)
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Prediction period ({days_ahead} days) exceeds your tier limit ({tier_config['max_days_ahead']} days). Upgrade your subscription."
                )
        
        elif operation == "feature_access":
            feature = kwargs.get("feature")
            if not self.check_feature_access(user, feature):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Feature '{feature}' not available in your subscription tier. Upgrade to access this feature."
                )


def get_subscription_service(db: Session) -> SubscriptionService:
    """Get subscription service instance"""
    return SubscriptionService(db)
