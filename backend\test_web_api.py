#!/usr/bin/env python3
"""Test the web API directly to see what data is being returned"""

import requests
import json

def test_web_api():
    print('=== TESTING WEB API DIRECTLY ===')
    print()
    
    base_url = "http://localhost:8000"
    
    # Test 1: Check if server is running
    print('1. Server Health Check:')
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print('✅ Server is running')
        else:
            print(f'❌ Server returned status {response.status_code}')
    except Exception as e:
        print(f'❌ Server not accessible: {e}')
        return
    print()
    
    # Test 2: Try to get prediction without auth (should fail)
    print('2. Prediction Without Auth (should fail):')
    try:
        response = requests.post(
            f"{base_url}/api/v1/stocks/AAPL/predict",
            json={"days_ahead": 3},
            timeout=30
        )
        print(f'Status: {response.status_code}')
        print(f'Response: {response.text}')
    except Exception as e:
        print(f'Error: {e}')
    print()
    
    # Test 3: Try to login
    print('3. Login Test:')
    try:
        # Try different login formats
        login_data = {
            "username": "<EMAIL>",
            "password": "admin123"
        }
        response = requests.post(
            f"{base_url}/api/v1/auth/login",
            data=login_data,  # Form data
            timeout=10
        )
        print(f'Login Status: {response.status_code}')
        if response.status_code == 200:
            token_data = response.json()
            print('✅ Login successful')
            access_token = token_data.get('access_token')
            
            # Test 4: Make authenticated prediction
            print()
            print('4. Authenticated Prediction Test:')
            headers = {"Authorization": f"Bearer {access_token}"}
            pred_response = requests.post(
                f"{base_url}/api/v1/stocks/AAPL/predict",
                json={"days_ahead": 3},
                headers=headers,
                timeout=60
            )
            print(f'Prediction Status: {pred_response.status_code}')
            if pred_response.status_code == 200:
                pred_data = pred_response.json()
                print('✅ Prediction successful!')
                print(f'Current Price: ${pred_data.get("current_price", "N/A")}')
                print(f'Predicted Prices: {pred_data.get("predicted_prices", [])}')
                print(f'Model Version: {pred_data.get("model_version", "N/A")}')
                print(f'Model Type: {pred_data.get("model_type", "N/A")}')
                print(f'Note: {pred_data.get("note", "N/A")}')
                
                # Check if it's real data
                current_price = pred_data.get("current_price", 0)
                if current_price != 150.0:
                    print('✅ Using REAL market data (not $150 mock)')
                else:
                    print('❌ Still using mock $150 data')
                    
                model_version = pred_data.get("model_version", "")
                if "pytorch" in model_version.lower():
                    print('✅ Using REAL PyTorch model')
                elif "trend" in model_version.lower():
                    print('⚠️  Using real trend analysis (fallback)')
                elif "mock" in model_version.lower():
                    print('❌ Still using mock model')
                else:
                    print(f'❓ Unknown model: {model_version}')
            else:
                print(f'❌ Prediction failed: {pred_response.text}')
        else:
            print(f'❌ Login failed: {response.text}')
    except Exception as e:
        print(f'❌ Login error: {e}')

if __name__ == "__main__":
    test_web_api()
