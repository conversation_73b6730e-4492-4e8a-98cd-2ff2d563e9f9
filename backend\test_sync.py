#!/usr/bin/env python3
"""Synchronous test to verify real implementation"""

def test_real_implementation():
    print('=== TESTING REAL IMPLEMENTATION ===')
    print()
    
    # Test 1: PyTorch availability
    print('1. PyTorch Import Test:')
    try:
        import torch
        print(f'✅ PyTorch version: {torch.__version__}')
        print(f'✅ CUDA available: {torch.cuda.is_available()}')
    except ImportError as e:
        print(f'❌ PyTorch import failed: {e}')
    print()
    
    # Test 2: Real market data
    print('2. Real Market Data Test:')
    try:
        import yfinance as yf
        ticker = yf.Ticker('AAPL')
        data = ticker.history(period='5d')
        if not data.empty:
            current_price = data['Close'].iloc[-1]
            print(f'✅ AAPL current price: ${current_price:.2f}')
            print(f'✅ Data points: {len(data)} days')
        else:
            print('❌ No market data retrieved')
    except Exception as e:
        print(f'❌ Market data error: {e}')
    print()
    
    # Test 3: Service import
    print('3. Service Import Test:')
    try:
        from app.services.stock_service import StockService, PYTORCH_AVAILABLE
        print(f'✅ StockService imported')
        print(f'✅ PyTorch available in service: {PYTORCH_AVAILABLE}')
        
        service = StockService()
        print(f'✅ StockService instance created')
    except Exception as e:
        print(f'❌ Service import error: {e}')
        import traceback
        traceback.print_exc()
    print()
    
    print('=== TEST COMPLETE ===')

if __name__ == "__main__":
    test_real_implementation()
