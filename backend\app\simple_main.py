"""
Simple FastAPI application for testing without complex dependencies
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, timedelta
import json

# Create FastAPI application
app = FastAPI(
    title="TradingBot SAAS ML Platform",
    description="SAAS Machine Learning Platform for Stock and Crypto Price Prediction",
    version="1.0.0",
)

# CORS middleware - Allow all origins for development
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=False,  # Set to False when using allow_origins=["*"]
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Pydantic models
class UserLogin(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str
    expires_in: int

class UserResponse(BaseModel):
    id: int
    email: str
    full_name: Optional[str]
    is_active: bool
    subscription_tier: str
    created_at: datetime

class StockDataResponse(BaseModel):
    symbol: str
    name: str
    current_price: float
    change_percent: float
    volume: int
    market_cap: Optional[float]
    last_updated: datetime

class StockPredictionRequest(BaseModel):
    days_ahead: int = 30
    include_technical_indicators: bool = True

class StockPredictionResponse(BaseModel):
    symbol: str
    current_price: float
    predicted_prices: List[float]
    prediction_dates: List[str]
    confidence_score: float
    model_version: str
    created_at: datetime

# Mock data
MOCK_STOCKS = [
    {"symbol": "AAPL", "name": "Apple Inc.", "exchange": "NASDAQ"},
    {"symbol": "GOOGL", "name": "Alphabet Inc.", "exchange": "NASDAQ"},
    {"symbol": "MSFT", "name": "Microsoft Corporation", "exchange": "NASDAQ"},
    {"symbol": "AMZN", "name": "Amazon.com Inc.", "exchange": "NASDAQ"},
    {"symbol": "TSLA", "name": "Tesla Inc.", "exchange": "NASDAQ"},
]

# Root endpoints
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "TradingBot SAAS ML Platform",
        "version": "1.0.0",
        "status": "active",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "tradingbot-api"}

@app.get("/test-cors")
async def test_cors():
    """Test CORS endpoint"""
    return {"message": "CORS is working", "timestamp": datetime.now().isoformat()}

# Authentication endpoints
@app.post("/api/v1/auth/token", response_model=Token)
async def login_for_access_token(form_data: UserLogin):
    """Login and get access token"""
    # Demo credentials
    if form_data.username == "<EMAIL>" and form_data.password == "demo123":
        return Token(
            access_token="demo_access_token_12345",
            token_type="bearer",
            expires_in=1800
        )
    raise HTTPException(status_code=401, detail="Incorrect email or password")

@app.options("/api/v1/auth/login")
async def login_options(response: Response):
    """Handle CORS preflight for login"""
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "POST, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
    return {"message": "OK"}

@app.post("/api/v1/auth/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends(), response: Response = None):
    """Login and get access token (alias for /token)"""
    # Demo credentials
    if form_data.username == "<EMAIL>" and form_data.password == "demo123":
        if response:
            response.headers["Access-Control-Allow-Origin"] = "*"
        return Token(
            access_token="demo_access_token_12345",
            token_type="bearer",
            expires_in=1800
        )
    raise HTTPException(status_code=401, detail="Incorrect email or password")

@app.get("/api/v1/auth/me", response_model=UserResponse)
async def get_current_user():
    """Get current user info"""
    return UserResponse(
        id=1,
        email="<EMAIL>",
        full_name="Demo User",
        is_active=True,
        subscription_tier="free",
        created_at=datetime.now()
    )

# Stock endpoints
@app.get("/api/v1/stocks/search")
async def search_stocks(query: str, limit: int = 10):
    """Search for stocks by symbol or company name"""
    query_lower = query.lower()
    filtered_stocks = [
        stock for stock in MOCK_STOCKS
        if query_lower in stock["symbol"].lower() or query_lower in stock["name"].lower()
    ]
    return filtered_stocks[:limit]

@app.get("/api/v1/stocks/{symbol}/data", response_model=StockDataResponse)
async def get_stock_data(symbol: str):
    """Get current stock data"""
    return StockDataResponse(
        symbol=symbol.upper(),
        name=f"{symbol.upper()} Company",
        current_price=150.25,
        change_percent=2.5,
        volume=1000000,
        market_cap=1000000000.0,
        last_updated=datetime.now()
    )

@app.post("/api/v1/stocks/{symbol}/predict", response_model=StockPredictionResponse)
async def predict_stock_price(symbol: str, request: StockPredictionRequest):
    """Generate stock price predictions using LSTM model"""
    try:
        # Try to use the actual LSTM prediction service
        from app.services.stock_service import stock_service

        prediction_result = await stock_service.predict_stock_price(
            symbol=symbol,
            days_ahead=request.days_ahead
        )

        return StockPredictionResponse(
            symbol=prediction_result["symbol"],
            current_price=prediction_result["current_price"],
            predicted_prices=prediction_result["predicted_prices"],
            prediction_dates=prediction_result["prediction_dates"],
            confidence_score=prediction_result["confidence_score"],
            model_version=prediction_result["model_version"],
            created_at=prediction_result["created_at"]
        )

    except Exception as e:
        # Fallback to mock data if LSTM prediction fails
        from datetime import timedelta

        base_date = datetime.now()
        prediction_dates = [(base_date + timedelta(days=i)).strftime("%Y-%m-%d") for i in range(1, request.days_ahead + 1)]
        predicted_prices = [150.0 + i * 0.5 for i in range(request.days_ahead)]  # Mock data

        return StockPredictionResponse(
            symbol=symbol.upper(),
            current_price=150.25,
            predicted_prices=predicted_prices,
            prediction_dates=prediction_dates,
            confidence_score=0.3,  # Lower confidence for fallback
            model_version="fallback_v1.0.0",
            created_at=datetime.now()
        )


@app.post("/api/v1/stocks/{symbol}/train")
async def train_stock_model(symbol: str, force_retrain: bool = False):
    """Train LSTM model for a specific stock symbol"""
    try:
        from app.ml.stocks.predictor import stock_predictor

        training_result = await stock_predictor.train_model(
            symbol=symbol,
            force_retrain=force_retrain
        )

        return training_result

    except Exception as e:
        return {
            "status": "error",
            "symbol": symbol,
            "error": str(e),
            "message": "Training failed, using fallback prediction model"
        }


@app.get("/api/v1/stocks/{symbol}/model-info")
async def get_stock_model_info(symbol: str):
    """Get information about the LSTM model for a stock symbol"""
    try:
        from app.ml.stocks.predictor import stock_predictor

        model_info = stock_predictor.get_model_info(symbol)
        return model_info

    except Exception as e:
        return {
            "symbol": symbol,
            "model_exists": False,
            "error": str(e),
            "message": "Model info not available"
        }

@app.get("/api/v1/stocks/{symbol}/history")
async def get_stock_history(symbol: str):
    """Get historical stock data"""
    # Mock historical data
    mock_data = []
    base_date = datetime.now()
    for i in range(30):
        date = base_date - timedelta(days=i)
        mock_data.append({
            "date": date.strftime("%Y-%m-%d"),
            "open": 148.0 + i * 0.1,
            "high": 152.0 + i * 0.1,
            "low": 146.0 + i * 0.1,
            "close": 150.0 + i * 0.1,
            "volume": 1000000 + i * 10000
        })
    
    return {
        "symbol": symbol.upper(),
        "data": mock_data,
        "interval": "1d"
    }

# Crypto endpoints
@app.get("/api/v1/crypto/search")
async def search_crypto(query: str, limit: int = 10):
    """Search for cryptocurrencies"""
    from app.services.crypto_service import crypto_service
    return await crypto_service.search_cryptocurrencies(query, limit)

@app.get("/api/v1/crypto/{symbol}/data")
async def get_crypto_data(symbol: str):
    """Get current cryptocurrency data"""
    from app.services.crypto_service import crypto_service

    data = await crypto_service.get_crypto_data(symbol)
    if not data:
        raise HTTPException(status_code=404, detail=f"Cryptocurrency {symbol} not found")

    return {
        "symbol": data["symbol"],
        "name": data["name"],
        "current_price": data["current_price"],
        "change_percent_24h": data["change_percent"],
        "volume_24h": data["volume_24h"],
        "market_cap": data.get("market_cap"),
        "last_updated": data["last_updated"]
    }

@app.post("/api/v1/crypto/{symbol}/predict")
async def predict_crypto_price(symbol: str, request: StockPredictionRequest):
    """Generate crypto price predictions using LSTM model"""
    try:
        from app.services.crypto_service import crypto_service

        # Use LSTM prediction service
        prediction_result = await crypto_service.predict_crypto_price(symbol, request.days_ahead)

        return {
            "symbol": prediction_result["symbol"],
            "current_price": prediction_result["current_price"],
            "predicted_prices": prediction_result["predicted_prices"],
            "prediction_dates": prediction_result["prediction_dates"],
            "confidence_score": prediction_result["confidence_score"],
            "volatility_score": prediction_result["volatility_score"],
            "model_version": prediction_result["model_version"],
            "created_at": prediction_result["created_at"]
        }

    except Exception as e:
        # Fallback to mock data if LSTM prediction fails
        base_date = datetime.now()
        prediction_dates = [(base_date + timedelta(days=i)).strftime("%Y-%m-%d") for i in range(1, request.days_ahead + 1)]
        predicted_prices = [45000.0 + i * 50.0 for i in range(request.days_ahead)]  # Mock data

        return {
            "symbol": symbol.upper(),
            "current_price": 45000.0,
            "predicted_prices": predicted_prices,
            "prediction_dates": prediction_dates,
            "confidence_score": 0.5,
            "volatility_score": 0.8,
            "model_version": "crypto_fallback_v1.0.0",
            "created_at": datetime.now(),
            "note": "Fallback prediction due to error"
        }

@app.post("/api/v1/crypto/{symbol}/train")
async def train_crypto_model(symbol: str):
    """Train LSTM model for cryptocurrency prediction"""
    try:
        from app.ml.crypto.predictor import crypto_predictor
        result = await crypto_predictor.train_model(symbol, force_retrain=True)
        return result
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.get("/api/v1/crypto/{symbol}/model-info")
async def get_crypto_model_info(symbol: str):
    """Get information about the LSTM model for a cryptocurrency"""
    try:
        from app.ml.crypto.predictor import crypto_predictor
        return crypto_predictor.get_model_info(symbol)
    except Exception as e:
        return {"error": str(e)}

if __name__ == "__main__":
    import uvicorn
    print("Starting FastAPI server...")
    print("Server will be available at: http://localhost:8000")
    print("API documentation at: http://localhost:8000/docs")
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8000,
        log_level="debug"
    )
